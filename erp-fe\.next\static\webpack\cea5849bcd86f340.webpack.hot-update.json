{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CStudyWork%5CTTMI%5CERP%5Cerp-fe%5Cnode_modules%5C.pnpm%5Cnext%4013.5.8_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cdist%5Cshared%5Clib%5Clazy-dynamic%5Cdynamic-no-ssr.js&modules=D%3A%5CStudyWork%5CTTMI%5CERP%5Cerp-fe%5Cnode_modules%5C.pnpm%5Cnext%4013.5.8_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CStudyWork%5CTTMI%5CERP%5Cerp-fe%5Csrc%5Capp%5Cclient-providers.tsx&modules=D%3A%5CStudyWork%5CTTMI%5CERP%5Cerp-fe%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CStudyWork%5CTTMI%5CERP%5Cerp-fe%5Cnode_modules%5C.pnpm%5Cnext%4013.5.8_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Noto_Sans%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%2C%22vietnamese%22%5D%2C%22variable%22%3A%22--font-noto-sans%22%7D%5D%2C%22variableName%22%3A%22notoSans%22%7D&modules=D%3A%5CStudyWork%5CTTMI%5CERP%5Cerp-fe%5Csrc%5Ccomponents%5Ccustom%5Carito%5Capp-bar%5Cindex.tsx&modules=D%3A%5CStudyWork%5CTTMI%5CERP%5Cerp-fe%5Csrc%5Ccomponents%5Cproviders%5Cposthog-provider.tsx&modules=D%3A%5CStudyWork%5CTTMI%5CERP%5Cerp-fe%5Csrc%5Ccomponents%5Cui%5Ctoaster.tsx&server=false!"]}