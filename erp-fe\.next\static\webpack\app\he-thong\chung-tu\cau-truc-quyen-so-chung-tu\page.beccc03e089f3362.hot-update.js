"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/index.tsx":
/*!************************************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/index.tsx ***!
  \************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_custom_arito_custom_input_table__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/custom-input-table */ \"(app-pages-browser)/./src/components/custom/arito/custom-input-table/index.tsx\");\n/* harmony import */ var _InputTableTab_cols__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../InputTableTab/cols */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx\");\n\n\n\n\nconst SoHienTaiTab = (param)=>{\n    let { mode, rows, selectedRowUuid, onRowClick, onAddRow, onDeleteRow, onCopyRow, onPasteRow, onMoveRow, onCellValueChange } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table__WEBPACK_IMPORTED_MODULE_2__.InputTable, {\n        rows: rows,\n        onRowClick: onRowClick,\n        selectedRowId: selectedRowUuid || undefined,\n        columns: (0,_InputTableTab_cols__WEBPACK_IMPORTED_MODULE_3__.getSoHienTaiColumns)({\n            onCellValueChange\n        }),\n        getRowId: (row)=>(row === null || row === void 0 ? void 0 : row.uuid) || \"\",\n        actionButtons: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InputTableActionBar, {\n            mode: mode,\n            handleAddRow: onAddRow,\n            handleDeleteRow: onDeleteRow,\n            handleCopyRow: onCopyRow,\n            handlePasteRow: onPasteRow,\n            handleMoveRow: onMoveRow\n        }, void 0, false, void 0, void 0)\n    }, void 0, false, {\n        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\index.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SoHienTaiTab;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SoHienTaiTab);\nvar _c;\n$RefreshReg$(_c, \"SoHienTaiTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/index.tsx\n"));

/***/ })

});