import { useState, useEffect, useCallback } from 'react';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { AritoDialog, AritoForm } from '@/components/custom/arito';
import useInputTableRow from './InputTableTab/useInputTableRow';
import { AritoIcon } from '@/components/custom/arito/icon';
import { DonViCoSo, ChungTu } from '@/types/schemas';
import ConfirmDialog from '../ConfirmDialog';
import InputTableTab from './InputTableTab';
import { formSchema } from '../../schemas';
import SoHienTaiTab from './SoHienTaiTab';
import { FormMode } from '@/types/form';
import GeneralTab from './GeneralTab';
import BasicForm from './BasicForm';

// Interface for User data (matching InputTableTab)
interface User {
  id: string;
  uuid: string;
  username: string;
  first_name: string;
  email?: string;
}

interface FormDialogProps {
  mode: FormMode;
  open: boolean;
  onClose: () => void;

  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
  onWatchButtonClick?: () => void;

  // Parent component's submit handler - updated to use new data structure
  onSubmit?: (data: any) => void;

  initialData?: any;
}

function FormDialog({
  mode,
  open,
  onClose,
  onAddButtonClick,
  onCopyButtonClick,
  onDeleteButtonClick,
  onEditButtonClick,
  onSubmit,
  initialData
}: FormDialogProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [donVi, setDonVi] = useState<DonViCoSo | null>(null);

  // Hàm tạo dòng mới tùy chỉnh cho bảng "Chứng từ & Người sử dụng"
  const createGiaBanRow = useCallback(() => {
    const newRow = {
      uuid: String(Math.random()),
      ma_ct: '', // Mã chứng từ để hiển thị
      ten_ct: '', // Tên chứng từ để hiển thị
      user_id: '', // ID người sử dụng (internal)
      username: '', // Username để hiển thị
      first_name: '' // Tên người sử dụng để hiển thị
    };
    return newRow;
  }, []);

  // Hàm tạo dòng mới tùy chỉnh cho bảng "Số hiện tại"
  const createSoHienTaiRow = useCallback(() => {
    const newRow = {
      uuid: String(Math.random()),
      ngay: '', // Ngày
      so_hien_tai: '' // Số hiện tại
    };
    return newRow;
  }, []);

  // Sử dụng hook useInputTableRow cho tab "Chứng từ & Người sử dụng"
  const {
    rows: tableData,
    setRows, // Uncomment to use for setting table data from initialData
    handleRowClick,
    handleAddRow: originalHandleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange: originalHandleCellValueChange
  } = useInputTableRow<any>([], createGiaBanRow);

  const {
    rows: soHienTaiTableData,
    setRows: setSoHienTaiRows,
    handleRowClick: handleSoHienTaiRowClick,
    handleCellValueChange: originalHandleSoHienTaiCellValueChange
  } = useInputTableRow<any>([], createSoHienTaiRow);

  // Wrap handleAddRow to add logging
  const handleAddRow = useCallback(() => {
    originalHandleAddRow();
  }, [originalHandleAddRow]);

  // Wrap handleCellValueChange to add logging
  const handleCellValueChange = useCallback(
    (rowUuid: string, field: string, newValue: any) => {
      originalHandleCellValueChange(rowUuid, field, newValue);
    },
    [originalHandleCellValueChange]
  );

  // Wrap handlers for "Số hiện tại" tab
  const handleSoHienTaiCellValueChange = useCallback(
    (rowUuid: string, field: string, newValue: any) => {
      originalHandleSoHienTaiCellValueChange(rowUuid, field, newValue);
    },
    [originalHandleSoHienTaiCellValueChange]
  );

  // State for storing selected data following GeneralTab pattern
  const [selectedChungTuData, setSelectedChungTuData] = useState<Record<string, ChungTu>>({});
  const [selectedUserData, setSelectedUserData] = useState<Record<string, User>>({});

  // Callback handlers following GeneralTab pattern
  const handleChungTuSelect = useCallback((rowUuid: string, chungTu: ChungTu) => {
    setSelectedChungTuData(prev => {
      const updated = {
        ...prev,
        [rowUuid]: chungTu
      };
      return updated;
    });
  }, []);

  const handleUserSelect = useCallback((rowUuid: string, user: User) => {
    setSelectedUserData(prev => {
      const updated = {
        ...prev,
        [rowUuid]: user
      };

      return updated;
    });
  }, []);

  const handleSubmit = async (data: any) => {
    console.log('Form data>>>>>>>>>>>:', data);
    console.log('Table data>>>>>>>>>>>:', tableData);

    // Create combined list with ma_ct (UUID) and username format for "Chứng từ & Người sử dụng" tab
    const combinedList = tableData.map(row => {
      const chungTu = selectedChungTuData[row.uuid];
      const user = selectedUserData[row.uuid];

      return {
        ma_ct: chungTu?.uuid || '', // Use ChungTu UUID instead of ma_ct
        username: user?.username || row.username || ''
      };
    });

    // Create combined list for "Số hiện tại" tab
    const soHienTaiCombinedList = soHienTaiTableData.map(row => {
      return {
        ngay: row.ngay || '',
        so_hien_tai: row.so_hien_tai || ''
      };
    });

    // Log the combined lists in requested format
    console.log('📋👥 List of ChungTu and User:', combinedList);

    try {
      if (onSubmit) {
        const formData = {
          ...data,
          danh_sach_chung_tu: combinedList,
          danh_sach_so_hien_tai: soHienTaiCombinedList
        };
        await onSubmit(formData);
      } else {
        setError('Không thể lưu dữ liệu: Không có xử lý submit');
      }
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra');
    }
  };

  const handleCloseDialog = () => {
    setShowConfirmDialog(false);
    onClose();
  };

  // Effect to update form fields when initialData changes
  useEffect(() => {
    if (initialData) {
      // Process initialData.danh_sach_chung_tu if available
      if (
        initialData.danh_sach_chung_tu &&
        Array.isArray(initialData.danh_sach_chung_tu) &&
        initialData.danh_sach_chung_tu.length > 0
      ) {
        // Convert danh_sach_chung_tu array to the format expected by the table
        const tableRows = initialData.danh_sach_chung_tu.map((item: any) => {
          // Create a new row with a unique UUID
          const newRow = createGiaBanRow();

          // Set the values from the danh_sach_chung_tu item
          newRow.ma_ct = item.ma_ct || '';
          newRow.ten_ct = item.ten_ct || '';
          newRow.user_id = item.id || '';
          newRow.username = item.username || '';
          newRow.first_name = item.first_name || '';

          // Store the selected data for form submission (following GeneralTab pattern)
          if (item.ma_ct) {
            setSelectedChungTuData(prev => ({
              ...prev,
              [newRow.uuid]: item // Store the full item data
            }));
          }

          if (item.username) {
            setSelectedUserData(prev => ({
              ...prev,
              [newRow.uuid]: item // Store the full item data
            }));
          }

          return newRow;
        });

        // Set the table data
        if (tableRows.length > 0) {
          // Use the setRows function from useInputTableRow
          setRows(tableRows);
        }
      }

      // Process initialData.danh_sach_so_hien_tai if available
      if (
        initialData.danh_sach_so_hien_tai &&
        Array.isArray(initialData.danh_sach_so_hien_tai) &&
        initialData.danh_sach_so_hien_tai.length > 0
      ) {
        // Convert danh_sach_so_hien_tai array to the format expected by the table
        const soHienTaiTableRows = initialData.danh_sach_so_hien_tai.map((item: any) => {
          // Create a new row with a unique UUID
          const newRow = createSoHienTaiRow();

          // Set the values from the danh_sach_so_hien_tai item
          newRow.ngay = item.ngay || '';
          newRow.so_hien_tai = item.so_hien_tai || '';

          return newRow;
        });

        // Set the table data for "Số hiện tại" tab
        if (soHienTaiTableRows.length > 0) {
          setSoHienTaiRows(soHienTaiTableRows);
        }
      }
    }
  }, [initialData, setRows, setSoHienTaiRows, createGiaBanRow, createSoHienTaiRow]);

  return (
    <>
      <AritoDialog
        open={open}
        onClose={onClose}
        title={mode === 'add' ? 'Mới' : mode === 'edit' ? 'Sửa' : 'Xem'}
        maxWidth='lg'
        disableBackdropClose={false}
        disableEscapeKeyDown={false}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={formSchema}
          onSubmit={data => {
            handleSubmit(data);
          }}
          initialData={initialData}
          className='w-[50vw]'
          headerFields={<BasicForm mode={mode} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          tabs={[
            {
              id: '1',
              label: 'Thông tin chung',
              component: <GeneralTab mode={mode} donVi={donVi} setDonVi={setDonVi} />
            },
            {
              id: '3',
              label: 'Chứng từ & Người sử dụng',
              component: (
                <InputTableTab
                  mode={mode}
                  rows={tableData}
                  onRowClick={handleRowClick}
                  onAddRow={handleAddRow}
                  onDeleteRow={handleDeleteRow}
                  onCopyRow={handleCopyRow}
                  onPasteRow={handlePasteRow}
                  onMoveRow={handleMoveRow}
                  onCellValueChange={handleCellValueChange}
                  onChungTuSelect={handleChungTuSelect}
                  onUserSelect={handleUserSelect}
                />
              )
            },
            {
              id: '4',
              label: 'Số hiện tại',
              component: (
                <SoHienTaiTab
                  mode={mode}
                  rows={soHienTaiTableData}
                  onRowClick={handleSoHienTaiRowClick}
                  onCellValueChange={handleSoHienTaiCellValueChange}
                />
              )
            }
          ]}
          bottomBar={
            <BottomBar
              mode={mode}
              onAdd={onAddButtonClick}
              onEdit={onEditButtonClick}
              onDelete={onDeleteButtonClick}
              onCopy={onCopyButtonClick}
              onClose={onClose}
            />
          }
        />
        {error && <div className='mx-4 mb-4 rounded bg-red-100 p-2 text-red-700'>{error}</div>}
      </AritoDialog>

      <ConfirmDialog
        onClose={handleCloseDialog}
        open={showConfirmDialog}
        onCloseConfirmDialog={() => setShowConfirmDialog(false)}
      />
    </>
  );
}

export default FormDialog;
