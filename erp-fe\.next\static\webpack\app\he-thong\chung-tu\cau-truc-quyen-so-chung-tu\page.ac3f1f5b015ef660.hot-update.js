"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx":
/*!***********************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx ***!
  \***********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_custom_arito_form_bottom_bar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/form/bottom-bar */ \"(app-pages-browser)/./src/components/custom/arito/form/bottom-bar.tsx\");\n/* harmony import */ var _components_custom_arito__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito */ \"(app-pages-browser)/./src/components/custom/arito/index.ts\");\n/* harmony import */ var _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./InputTableTab/useInputTableRow */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/useInputTableRow.ts\");\n/* harmony import */ var _components_custom_arito_icon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/custom/arito/icon */ \"(app-pages-browser)/./src/components/custom/arito/icon/index.tsx\");\n/* harmony import */ var _ConfirmDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ConfirmDialog */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/ConfirmDialog.tsx\");\n/* harmony import */ var _InputTableTab__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./InputTableTab */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/index.tsx\");\n/* harmony import */ var _SoHienTaiTab__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./SoHienTaiTab */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/index.tsx\");\n/* harmony import */ var _schemas__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../schemas */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/schemas.ts\");\n/* harmony import */ var _GeneralTab__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./GeneralTab */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/GeneralTab.tsx\");\n/* harmony import */ var _BasicForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./BasicForm */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/BasicForm.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction FormDialog(param) {\n    let { mode, open, onClose, onAddButtonClick, onCopyButtonClick, onDeleteButtonClick, onEditButtonClick, onSubmit, initialData } = param;\n    _s();\n    const [showConfirmDialog, setShowConfirmDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [donVi, setDonVi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Hàm tạo dòng mới tùy chỉnh cho bảng \"Chứng từ & Người sử dụng\"\n    const createGiaBanRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newRow = {\n            uuid: String(Math.random()),\n            ma_ct: \"\",\n            ten_ct: \"\",\n            user_id: \"\",\n            username: \"\",\n            first_name: \"\" // Tên người sử dụng để hiển thị\n        };\n        return newRow;\n    }, []);\n    // Hàm tạo dòng mới tùy chỉnh cho bảng \"Số hiện tại\"\n    const createSoHienTaiRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newRow = {\n            uuid: String(Math.random()),\n            ngay: \"\",\n            so_hien_tai: \"\" // Số hiện tại\n        };\n        return newRow;\n    }, []);\n    // Sử dụng hook useInputTableRow cho tab \"Chứng từ & Người sử dụng\"\n    const { rows: tableData, setRows, handleRowClick, handleAddRow: originalHandleAddRow, handleDeleteRow, handleCopyRow, handlePasteRow, handleMoveRow, handleCellValueChange: originalHandleCellValueChange } = (0,_InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([], createGiaBanRow);\n    // Sử dụng hook useInputTableRow cho tab \"Số hiện tại\"\n    const { rows: soHienTaiTableData, setRows: setSoHienTaiRows, handleRowClick: handleSoHienTaiRowClick, handleAddRow: originalHandleSoHienTaiAddRow, handleDeleteRow: handleSoHienTaiDeleteRow, handleCopyRow: handleSoHienTaiCopyRow, handlePasteRow: handleSoHienTaiPasteRow, handleMoveRow: handleSoHienTaiMoveRow, handleCellValueChange: originalHandleSoHienTaiCellValueChange } = (0,_InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([], createSoHienTaiRow);\n    // Wrap handleAddRow to add logging\n    const handleAddRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        originalHandleAddRow();\n    }, [\n        originalHandleAddRow\n    ]);\n    // Wrap handleCellValueChange to add logging\n    const handleCellValueChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, field, newValue)=>{\n        originalHandleCellValueChange(rowUuid, field, newValue);\n    }, [\n        originalHandleCellValueChange\n    ]);\n    // Wrap handlers for \"Số hiện tại\" tab\n    const handleSoHienTaiAddRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        originalHandleSoHienTaiAddRow();\n    }, [\n        originalHandleSoHienTaiAddRow\n    ]);\n    const handleSoHienTaiCellValueChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, field, newValue)=>{\n        originalHandleSoHienTaiCellValueChange(rowUuid, field, newValue);\n    }, [\n        originalHandleSoHienTaiCellValueChange\n    ]);\n    // State for storing selected data following GeneralTab pattern\n    const [selectedChungTuData, setSelectedChungTuData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedUserData, setSelectedUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // State for storing selected data for \"Số hiện tại\" tab\n    const [selectedSoHienTaiChungTuData, setSelectedSoHienTaiChungTuData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedSoHienTaiUserData, setSelectedSoHienTaiUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Callback handlers following GeneralTab pattern\n    const handleChungTuSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, chungTu)=>{\n        setSelectedChungTuData((prev)=>{\n            const updated = {\n                ...prev,\n                [rowUuid]: chungTu\n            };\n            return updated;\n        });\n    }, []);\n    const handleUserSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, user)=>{\n        setSelectedUserData((prev)=>{\n            const updated = {\n                ...prev,\n                [rowUuid]: user\n            };\n            return updated;\n        });\n    }, []);\n    // Callback handlers for \"Số hiện tại\" tab\n    const handleSoHienTaiChungTuSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, chungTu)=>{\n        setSelectedSoHienTaiChungTuData((prev)=>{\n            const updated = {\n                ...prev,\n                [rowUuid]: chungTu\n            };\n            return updated;\n        });\n    }, []);\n    const handleSoHienTaiUserSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, user)=>{\n        setSelectedSoHienTaiUserData((prev)=>{\n            const updated = {\n                ...prev,\n                [rowUuid]: user\n            };\n            return updated;\n        });\n    }, []);\n    const handleSubmit = async (data)=>{\n        console.log(\"Form data>>>>>>>>>>>:\", data);\n        console.log(\"Table data>>>>>>>>>>>:\", tableData);\n        console.log(\"Số hiện tại table data>>>>>>>>>>>:\", soHienTaiTableData);\n        // Create combined list with ma_ct (UUID) and username format for \"Chứng từ & Người sử dụng\" tab\n        const combinedList = tableData.map((row)=>{\n            const chungTu = selectedChungTuData[row.uuid];\n            const user = selectedUserData[row.uuid];\n            return {\n                ma_ct: (chungTu === null || chungTu === void 0 ? void 0 : chungTu.uuid) || \"\",\n                username: (user === null || user === void 0 ? void 0 : user.username) || row.username || \"\"\n            };\n        });\n        // Create combined list for \"Số hiện tại\" tab\n        const soHienTaiCombinedList = soHienTaiTableData.map((row)=>{\n            return {\n                ngay: row.ngay || \"\",\n                so_hien_tai: row.so_hien_tai || \"\"\n            };\n        });\n        // Log the combined lists in requested format\n        console.log(\"\\uD83D\\uDCCB\\uD83D\\uDC65 List of ChungTu and User:\", combinedList);\n        console.log(\"\\uD83D\\uDCCB\\uD83D\\uDC65 List of Số hiện tại ChungTu and User:\", soHienTaiCombinedList);\n        try {\n            if (onSubmit) {\n                const formData = {\n                    ...data,\n                    danh_sach_chung_tu: combinedList,\n                    danh_sach_so_hien_tai: soHienTaiCombinedList\n                };\n                await onSubmit(formData);\n            } else {\n                setError(\"Kh\\xf4ng thể lưu dữ liệu: Kh\\xf4ng c\\xf3 xử l\\xfd submit\");\n            }\n        } catch (err) {\n            setError(err.message || \"C\\xf3 lỗi xảy ra\");\n        }\n    };\n    const handleCloseDialog = ()=>{\n        setShowConfirmDialog(false);\n        onClose();\n    };\n    // Effect to update form fields when initialData changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialData) {\n            // Process initialData.danh_sach_chung_tu if available\n            if (initialData.danh_sach_chung_tu && Array.isArray(initialData.danh_sach_chung_tu) && initialData.danh_sach_chung_tu.length > 0) {\n                // Convert danh_sach_chung_tu array to the format expected by the table\n                const tableRows = initialData.danh_sach_chung_tu.map((item)=>{\n                    // Create a new row with a unique UUID\n                    const newRow = createGiaBanRow();\n                    // Set the values from the danh_sach_chung_tu item\n                    newRow.ma_ct = item.ma_ct || \"\";\n                    newRow.ten_ct = item.ten_ct || \"\";\n                    newRow.user_id = item.id || \"\";\n                    newRow.username = item.username || \"\";\n                    newRow.first_name = item.first_name || \"\";\n                    // Store the selected data for form submission (following GeneralTab pattern)\n                    if (item.ma_ct) {\n                        setSelectedChungTuData((prev)=>({\n                                ...prev,\n                                [newRow.uuid]: item // Store the full item data\n                            }));\n                    }\n                    if (item.username) {\n                        setSelectedUserData((prev)=>({\n                                ...prev,\n                                [newRow.uuid]: item // Store the full item data\n                            }));\n                    }\n                    return newRow;\n                });\n                // Set the table data\n                if (tableRows.length > 0) {\n                    // Use the setRows function from useInputTableRow\n                    setRows(tableRows);\n                }\n            }\n            // Process initialData.danh_sach_so_hien_tai if available\n            if (initialData.danh_sach_so_hien_tai && Array.isArray(initialData.danh_sach_so_hien_tai) && initialData.danh_sach_so_hien_tai.length > 0) {\n                // Convert danh_sach_so_hien_tai array to the format expected by the table\n                const soHienTaiTableRows = initialData.danh_sach_so_hien_tai.map((item)=>{\n                    // Create a new row with a unique UUID\n                    const newRow = createSoHienTaiRow();\n                    // Set the values from the danh_sach_so_hien_tai item\n                    newRow.ngay = item.ngay || \"\";\n                    newRow.so_hien_tai = item.so_hien_tai || \"\";\n                    return newRow;\n                });\n                // Set the table data for \"Số hiện tại\" tab\n                if (soHienTaiTableRows.length > 0) {\n                    // Use the setSoHienTaiRows function from useInputTableRow\n                    setSoHienTaiRows(soHienTaiTableRows);\n                }\n            }\n        }\n    }, [\n        initialData,\n        setRows,\n        setSoHienTaiRows,\n        createGiaBanRow\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_3__.AritoDialog, {\n                open: open,\n                onClose: onClose,\n                title: mode === \"add\" ? \"Mới\" : mode === \"edit\" ? \"Sửa\" : \"Xem\",\n                maxWidth: \"lg\",\n                disableBackdropClose: false,\n                disableEscapeKeyDown: false,\n                titleIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_icon__WEBPACK_IMPORTED_MODULE_5__.AritoIcon, {\n                    icon: 281\n                }, void 0, false, void 0, void 0),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_3__.AritoForm, {\n                        mode: mode,\n                        hasAritoActionBar: false,\n                        schema: _schemas__WEBPACK_IMPORTED_MODULE_9__.formSchema,\n                        onSubmit: (data)=>{\n                            handleSubmit(data);\n                        },\n                        initialData: initialData,\n                        className: \"w-[50vw]\",\n                        headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BasicForm__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            mode: mode\n                        }, void 0, false, void 0, void 0),\n                        classNameBottomBar: \"relative w-full flex justify-end gap-2\",\n                        tabs: [\n                            {\n                                id: \"1\",\n                                label: \"Th\\xf4ng tin chung\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GeneralTab__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    mode: mode,\n                                    donVi: donVi,\n                                    setDonVi: setDonVi\n                                }, void 0, false, void 0, void 0)\n                            },\n                            {\n                                id: \"3\",\n                                label: \"Chứng từ & Người sử dụng\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputTableTab__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    mode: mode,\n                                    rows: tableData,\n                                    onRowClick: handleRowClick,\n                                    onAddRow: handleAddRow,\n                                    onDeleteRow: handleDeleteRow,\n                                    onCopyRow: handleCopyRow,\n                                    onPasteRow: handlePasteRow,\n                                    onMoveRow: handleMoveRow,\n                                    onCellValueChange: handleCellValueChange,\n                                    onChungTuSelect: handleChungTuSelect,\n                                    onUserSelect: handleUserSelect\n                                }, void 0, false, void 0, void 0)\n                            },\n                            {\n                                id: \"4\",\n                                label: \"Số hiện tại\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SoHienTaiTab__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    mode: mode,\n                                    rows: soHienTaiTableData,\n                                    onRowClick: handleSoHienTaiRowClick,\n                                    onAddRow: handleSoHienTaiAddRow,\n                                    onDeleteRow: handleSoHienTaiDeleteRow,\n                                    onCopyRow: handleSoHienTaiCopyRow,\n                                    onPasteRow: handleSoHienTaiPasteRow,\n                                    onMoveRow: handleSoHienTaiMoveRow,\n                                    onCellValueChange: handleSoHienTaiCellValueChange\n                                }, void 0, false, void 0, void 0)\n                            }\n                        ],\n                        bottomBar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_bottom_bar__WEBPACK_IMPORTED_MODULE_2__.BottomBar, {\n                            mode: mode,\n                            onAdd: onAddButtonClick,\n                            onEdit: onEditButtonClick,\n                            onDelete: onDeleteButtonClick,\n                            onCopy: onCopyButtonClick,\n                            onClose: onClose\n                        }, void 0, false, void 0, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-4 mb-4 rounded bg-red-100 p-2 text-red-700\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 19\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConfirmDialog__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onClose: handleCloseDialog,\n                open: showConfirmDialog,\n                onCloseConfirmDialog: ()=>setShowConfirmDialog(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                lineNumber: 384,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FormDialog, \"4w9sOHT+l7xnmZvzgBdVxtcjF1c=\", false, function() {\n    return [\n        _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = FormDialog;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FormDialog);\nvar _c;\n$RefreshReg$(_c, \"FormDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx\n"));

/***/ })

});