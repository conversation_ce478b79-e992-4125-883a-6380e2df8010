import { GridColDef } from '@mui/x-data-grid';
import React from 'react';
import { chungTuSearchColumns, userSearchColumns } from '@/constants/search-columns';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import QUERY_KEYS from '@/constants/query-keys';
import type { ChungTu } from '@/types/schemas';

// Interface for User data (simplified from backend structure)
interface User {
  id: string;
  uuid: string;
  username: string;
  first_name: string;
  email?: string;
}

// Props interface following GeneralTab pattern
interface InputTableColumnsProps {
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
  onChungTuSelect?: (rowUuid: string, chungTu: ChungTu) => void;
  onUserSelect?: (rowUuid: string, user: User) => void;
}

export const getInputTableColumns = ({
  onCellValueChange,
  onChungTuSelect,
  onUserSelect
}: InputTableColumnsProps): GridColDef[] => [
  {
    field: 'ma_ct',
    headerName: 'Mã chứng từ',
    width: 175,
    renderCell: params => {
      return (
        <SearchField<ChungTu>
          value={params.row.ma_ct || ''}
          onValueChange={(newValue: any) => {
            onCellValueChange(params.row.uuid, 'ma_ct', newValue);
          }}
          searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}`}
          searchColumns={chungTuSearchColumns}
          dialogTitle='Danh mục chứng từ'
          className='w-full'
          displayRelatedField='ten_ct'
          columnDisplay='ma_ct'
          relatedFieldValue={params.row.ten_ct || ''}
          onRowSelection={(selectedChungTu: ChungTu) => {
            if (selectedChungTu) {
              onCellValueChange(params.row.uuid, 'ma_ct', selectedChungTu.ma_ct);
              onCellValueChange(params.row.uuid, 'ten_ct', selectedChungTu.ten_ct || '');
              if (onChungTuSelect) {
                onChungTuSelect(params.row.uuid, selectedChungTu);
              }
            }
          }}
        />
      );
    }
  },
  {
    field: 'ten_ct',
    headerName: 'Tên chứng từ',
    width: 200,
    renderCell: params => (
      <CellField
        name='ten_ct'
        type='text'
        value={params.row.ten_ct}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ten_ct', newValue)}
        disabled={true}
      />
    )
  },
  {
    field: 'user_id',
    headerName: 'Người sử dụng Nhóm',
    width: 175,
    renderCell: params => {
      return (
        <SearchField<User>
          value={params.row.username || ''}
          onValueChange={(newValue: any) => {
            onCellValueChange(params.row.uuid, 'username', newValue);
            // Khi người dùng xóa giá trị, cũng xóa related field
            if (!newValue) {
              onCellValueChange(params.row.uuid, 'first_name', '');
            }
          }}
          otherSearchEndpoint={`/${QUERY_KEYS.NGUOI_SU_DUNG}`}
          searchEndpoint={``}
          searchColumns={userSearchColumns}
          dialogTitle='Danh mục người sử dụng'
          className='w-full'
          displayRelatedField='first_name'
          columnDisplay='username'
          relatedFieldValue={params.row.first_name || ''}
          onRowSelection={(selectedUser: User) => {
            if (selectedUser) {
              onCellValueChange(params.row.uuid, 'username', selectedUser.username);
              onCellValueChange(params.row.uuid, 'first_name', selectedUser.first_name);

              // Call parent callback for additional processing
              if (onUserSelect) {
                onUserSelect(params.row.uuid, selectedUser);
              }
            }
          }}
        />
      );
    }
  },
  {
    field: 'username',
    headerName: 'Tên người sử dụng',
    width: 200,
    renderCell: params => (
      <CellField
        name='first_name'
        type='text'
        value={params.row.first_name}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'first_name', newValue)}
        disabled={true}
      />
    )
  }
];

// Props interface for "Số hiện tại" columns
interface SoHienTaiColumnsProps {
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
}

export const getSoHienTaiColumns = ({
  onCellValueChange
}: SoHienTaiColumnsProps): GridColDef[] => [
  {
    field: 'ngay',
    headerName: 'Ngày',
    width: 200,
    renderCell: params => (
      <CellField
        name='ngay'
        type='date'
        value={params.row.ngay || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ngay', newValue)}
      />
    )
  },
  {
    field: 'so_hien_tai',
    headerName: 'Số hiện tại',
    width: 200,
    renderCell: params => (
      <CellField
        name='so_hien_tai'
        type='number'
        value={params.row.so_hien_tai || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'so_hien_tai', newValue)}
      />
    )
  }
];
