"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/index.tsx":
/*!************************************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/index.tsx ***!
  \************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_custom_arito_custom_input_table__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/custom-input-table */ \"(app-pages-browser)/./src/components/custom/arito/custom-input-table/index.tsx\");\n/* harmony import */ var _SoHienTaiActionBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SoHienTaiActionBar */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/SoHienTaiActionBar.tsx\");\n/* harmony import */ var _InputTableTab_cols__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../InputTableTab/cols */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx\");\n\n\n\n\n\nconst SoHienTaiTab = (param)=>{\n    let { mode, rows, selectedRowUuid, onRowClick, onCellValueChange } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table__WEBPACK_IMPORTED_MODULE_2__.InputTable, {\n        rows: rows,\n        onRowClick: onRowClick,\n        selectedRowId: selectedRowUuid || undefined,\n        columns: (0,_InputTableTab_cols__WEBPACK_IMPORTED_MODULE_4__.getSoHienTaiColumns)({\n            onCellValueChange\n        }),\n        getRowId: (row)=>(row === null || row === void 0 ? void 0 : row.uuid) || \"\",\n        actionButtons: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SoHienTaiActionBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            mode: mode\n        }, void 0, false, void 0, void 0)\n    }, void 0, false, {\n        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\index.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SoHienTaiTab;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SoHienTaiTab);\nvar _c;\n$RefreshReg$(_c, \"SoHienTaiTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/index.tsx\n"));

/***/ })

});