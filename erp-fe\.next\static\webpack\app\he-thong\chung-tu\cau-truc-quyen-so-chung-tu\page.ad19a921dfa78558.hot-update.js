"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx":
/*!***********************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx ***!
  \***********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_custom_arito_form_bottom_bar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/form/bottom-bar */ \"(app-pages-browser)/./src/components/custom/arito/form/bottom-bar.tsx\");\n/* harmony import */ var _components_custom_arito__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito */ \"(app-pages-browser)/./src/components/custom/arito/index.ts\");\n/* harmony import */ var _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./InputTableTab/useInputTableRow */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/useInputTableRow.ts\");\n/* harmony import */ var _components_custom_arito_icon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/custom/arito/icon */ \"(app-pages-browser)/./src/components/custom/arito/icon/index.tsx\");\n/* harmony import */ var _ConfirmDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ConfirmDialog */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/ConfirmDialog.tsx\");\n/* harmony import */ var _InputTableTab__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./InputTableTab */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/index.tsx\");\n/* harmony import */ var _schemas__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../schemas */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/schemas.ts\");\n/* harmony import */ var _GeneralTab__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./GeneralTab */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/GeneralTab.tsx\");\n/* harmony import */ var _BasicForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./BasicForm */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/BasicForm.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction FormDialog(param) {\n    let { mode, open, onClose, onAddButtonClick, onCopyButtonClick, onDeleteButtonClick, onEditButtonClick, onSubmit, initialData } = param;\n    _s();\n    const [showConfirmDialog, setShowConfirmDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [donVi, setDonVi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Hàm tạo dòng mới tùy chỉnh cho bảng \"Chứng từ & Người sử dụng\"\n    const createGiaBanRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newRow = {\n            uuid: String(Math.random()),\n            ma_ct: \"\",\n            ten_ct: \"\",\n            user_id: \"\",\n            username: \"\",\n            first_name: \"\" // Tên người sử dụng để hiển thị\n        };\n        return newRow;\n    }, []);\n    // Hàm tạo dòng mới tùy chỉnh cho bảng \"Số hiện tại\"\n    const createSoHienTaiRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newRow = {\n            uuid: String(Math.random()),\n            ngay: \"\",\n            so_hien_tai: \"\" // Số hiện tại\n        };\n        return newRow;\n    }, []);\n    // Sử dụng hook useInputTableRow cho tab \"Chứng từ & Người sử dụng\"\n    const { rows: tableData, setRows, handleRowClick, handleAddRow: originalHandleAddRow, handleDeleteRow, handleCopyRow, handlePasteRow, handleMoveRow, handleCellValueChange: originalHandleCellValueChange } = (0,_InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([], createGiaBanRow);\n    // Sử dụng hook useInputTableRow cho tab \"Số hiện tại\"\n    const { rows: soHienTaiTableData, setRows: setSoHienTaiRows, handleRowClick: handleSoHienTaiRowClick, handleAddRow: originalHandleSoHienTaiAddRow, handleDeleteRow: handleSoHienTaiDeleteRow, handleCopyRow: handleSoHienTaiCopyRow, handlePasteRow: handleSoHienTaiPasteRow, handleMoveRow: handleSoHienTaiMoveRow, handleCellValueChange: originalHandleSoHienTaiCellValueChange } = (0,_InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([], createSoHienTaiRow);\n    // Wrap handleAddRow to add logging\n    const handleAddRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        originalHandleAddRow();\n    }, [\n        originalHandleAddRow\n    ]);\n    // Wrap handleCellValueChange to add logging\n    const handleCellValueChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, field, newValue)=>{\n        originalHandleCellValueChange(rowUuid, field, newValue);\n    }, [\n        originalHandleCellValueChange\n    ]);\n    // Wrap handlers for \"Số hiện tại\" tab\n    const handleSoHienTaiAddRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        originalHandleSoHienTaiAddRow();\n    }, [\n        originalHandleSoHienTaiAddRow\n    ]);\n    const handleSoHienTaiCellValueChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, field, newValue)=>{\n        originalHandleSoHienTaiCellValueChange(rowUuid, field, newValue);\n    }, [\n        originalHandleSoHienTaiCellValueChange\n    ]);\n    // State for storing selected data following GeneralTab pattern\n    const [selectedChungTuData, setSelectedChungTuData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedUserData, setSelectedUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // State for storing selected data for \"Số hiện tại\" tab\n    const [selectedSoHienTaiChungTuData, setSelectedSoHienTaiChungTuData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedSoHienTaiUserData, setSelectedSoHienTaiUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Callback handlers following GeneralTab pattern\n    const handleChungTuSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, chungTu)=>{\n        setSelectedChungTuData((prev)=>{\n            const updated = {\n                ...prev,\n                [rowUuid]: chungTu\n            };\n            return updated;\n        });\n    }, []);\n    const handleUserSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, user)=>{\n        setSelectedUserData((prev)=>{\n            const updated = {\n                ...prev,\n                [rowUuid]: user\n            };\n            return updated;\n        });\n    }, []);\n    // Callback handlers for \"Số hiện tại\" tab\n    const handleSoHienTaiChungTuSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, chungTu)=>{\n        setSelectedSoHienTaiChungTuData((prev)=>{\n            const updated = {\n                ...prev,\n                [rowUuid]: chungTu\n            };\n            return updated;\n        });\n    }, []);\n    const handleSoHienTaiUserSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, user)=>{\n        setSelectedSoHienTaiUserData((prev)=>{\n            const updated = {\n                ...prev,\n                [rowUuid]: user\n            };\n            return updated;\n        });\n    }, []);\n    const handleSubmit = async (data)=>{\n        console.log(\"Form data>>>>>>>>>>>:\", data);\n        console.log(\"Table data>>>>>>>>>>>:\", tableData);\n        console.log(\"Số hiện tại table data>>>>>>>>>>>:\", soHienTaiTableData);\n        // Create combined list with ma_ct (UUID) and username format for \"Chứng từ & Người sử dụng\" tab\n        const combinedList = tableData.map((row)=>{\n            const chungTu = selectedChungTuData[row.uuid];\n            const user = selectedUserData[row.uuid];\n            return {\n                ma_ct: (chungTu === null || chungTu === void 0 ? void 0 : chungTu.uuid) || \"\",\n                username: (user === null || user === void 0 ? void 0 : user.username) || row.username || \"\"\n            };\n        });\n        // Create combined list for \"Số hiện tại\" tab\n        const soHienTaiCombinedList = soHienTaiTableData.map((row)=>{\n            const chungTu = selectedSoHienTaiChungTuData[row.uuid];\n            const user = selectedSoHienTaiUserData[row.uuid];\n            return {\n                ma_ct: (chungTu === null || chungTu === void 0 ? void 0 : chungTu.uuid) || \"\",\n                username: (user === null || user === void 0 ? void 0 : user.username) || row.username || \"\"\n            };\n        });\n        // Log the combined lists in requested format\n        console.log(\"\\uD83D\\uDCCB\\uD83D\\uDC65 List of ChungTu and User:\", combinedList);\n        console.log(\"\\uD83D\\uDCCB\\uD83D\\uDC65 List of Số hiện tại ChungTu and User:\", soHienTaiCombinedList);\n        try {\n            if (onSubmit) {\n                const formData = {\n                    ...data,\n                    danh_sach_chung_tu: combinedList,\n                    danh_sach_so_hien_tai: soHienTaiCombinedList\n                };\n                await onSubmit(formData);\n            } else {\n                setError(\"Kh\\xf4ng thể lưu dữ liệu: Kh\\xf4ng c\\xf3 xử l\\xfd submit\");\n            }\n        } catch (err) {\n            setError(err.message || \"C\\xf3 lỗi xảy ra\");\n        }\n    };\n    const handleCloseDialog = ()=>{\n        setShowConfirmDialog(false);\n        onClose();\n    };\n    // Effect to update form fields when initialData changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialData) {\n            // Process initialData.danh_sach_chung_tu if available\n            if (initialData.danh_sach_chung_tu && Array.isArray(initialData.danh_sach_chung_tu) && initialData.danh_sach_chung_tu.length > 0) {\n                // Convert danh_sach_chung_tu array to the format expected by the table\n                const tableRows = initialData.danh_sach_chung_tu.map((item)=>{\n                    // Create a new row with a unique UUID\n                    const newRow = createGiaBanRow();\n                    // Set the values from the danh_sach_chung_tu item\n                    newRow.ma_ct = item.ma_ct || \"\";\n                    newRow.ten_ct = item.ten_ct || \"\";\n                    newRow.user_id = item.id || \"\";\n                    newRow.username = item.username || \"\";\n                    newRow.first_name = item.first_name || \"\";\n                    // Store the selected data for form submission (following GeneralTab pattern)\n                    if (item.ma_ct) {\n                        setSelectedChungTuData((prev)=>({\n                                ...prev,\n                                [newRow.uuid]: item // Store the full item data\n                            }));\n                    }\n                    if (item.username) {\n                        setSelectedUserData((prev)=>({\n                                ...prev,\n                                [newRow.uuid]: item // Store the full item data\n                            }));\n                    }\n                    return newRow;\n                });\n                // Set the table data\n                if (tableRows.length > 0) {\n                    // Use the setRows function from useInputTableRow\n                    setRows(tableRows);\n                }\n            }\n            // Process initialData.danh_sach_so_hien_tai if available\n            if (initialData.danh_sach_so_hien_tai && Array.isArray(initialData.danh_sach_so_hien_tai) && initialData.danh_sach_so_hien_tai.length > 0) {\n                // Convert danh_sach_so_hien_tai array to the format expected by the table\n                const soHienTaiTableRows = initialData.danh_sach_so_hien_tai.map((item)=>{\n                    // Create a new row with a unique UUID\n                    const newRow = createGiaBanRow();\n                    // Set the values from the danh_sach_so_hien_tai item\n                    newRow.ma_ct = item.ma_ct || \"\";\n                    newRow.ten_ct = item.ten_ct || \"\";\n                    newRow.user_id = item.id || \"\";\n                    newRow.username = item.username || \"\";\n                    newRow.first_name = item.first_name || \"\";\n                    // Store the selected data for form submission (following GeneralTab pattern)\n                    if (item.ma_ct) {\n                        setSelectedSoHienTaiChungTuData((prev)=>({\n                                ...prev,\n                                [newRow.uuid]: item // Store the full item data\n                            }));\n                    }\n                    if (item.username) {\n                        setSelectedSoHienTaiUserData((prev)=>({\n                                ...prev,\n                                [newRow.uuid]: item // Store the full item data\n                            }));\n                    }\n                    return newRow;\n                });\n                // Set the table data for \"Số hiện tại\" tab\n                if (soHienTaiTableRows.length > 0) {\n                    // Use the setSoHienTaiRows function from useInputTableRow\n                    setSoHienTaiRows(soHienTaiTableRows);\n                }\n            }\n        }\n    }, [\n        initialData,\n        setRows,\n        setSoHienTaiRows,\n        createGiaBanRow\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_3__.AritoDialog, {\n                open: open,\n                onClose: onClose,\n                title: mode === \"add\" ? \"Mới\" : mode === \"edit\" ? \"Sửa\" : \"Xem\",\n                maxWidth: \"lg\",\n                disableBackdropClose: false,\n                disableEscapeKeyDown: false,\n                titleIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_icon__WEBPACK_IMPORTED_MODULE_5__.AritoIcon, {\n                    icon: 281\n                }, void 0, false, void 0, void 0),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_3__.AritoForm, {\n                        mode: mode,\n                        hasAritoActionBar: false,\n                        schema: _schemas__WEBPACK_IMPORTED_MODULE_8__.formSchema,\n                        onSubmit: (data)=>{\n                            handleSubmit(data);\n                        },\n                        initialData: initialData,\n                        className: \"w-[50vw]\",\n                        headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BasicForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            mode: mode\n                        }, void 0, false, void 0, void 0),\n                        classNameBottomBar: \"relative w-full flex justify-end gap-2\",\n                        tabs: [\n                            {\n                                id: \"1\",\n                                label: \"Th\\xf4ng tin chung\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GeneralTab__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    mode: mode,\n                                    donVi: donVi,\n                                    setDonVi: setDonVi\n                                }, void 0, false, void 0, void 0)\n                            },\n                            {\n                                id: \"3\",\n                                label: \"Chứng từ & Người sử dụng\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputTableTab__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    mode: mode,\n                                    rows: tableData,\n                                    onRowClick: handleRowClick,\n                                    onAddRow: handleAddRow,\n                                    onDeleteRow: handleDeleteRow,\n                                    onCopyRow: handleCopyRow,\n                                    onPasteRow: handlePasteRow,\n                                    onMoveRow: handleMoveRow,\n                                    onCellValueChange: handleCellValueChange,\n                                    onChungTuSelect: handleChungTuSelect,\n                                    onUserSelect: handleUserSelect\n                                }, void 0, false, void 0, void 0)\n                            },\n                            {\n                                id: \"4\",\n                                label: \"Số hiện tại\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputTableTab__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    mode: mode,\n                                    rows: soHienTaiTableData,\n                                    onRowClick: handleSoHienTaiRowClick,\n                                    onAddRow: handleSoHienTaiAddRow,\n                                    onDeleteRow: handleSoHienTaiDeleteRow,\n                                    onCopyRow: handleSoHienTaiCopyRow,\n                                    onPasteRow: handleSoHienTaiPasteRow,\n                                    onMoveRow: handleSoHienTaiMoveRow,\n                                    onCellValueChange: handleSoHienTaiCellValueChange,\n                                    onChungTuSelect: handleSoHienTaiChungTuSelect,\n                                    onUserSelect: handleSoHienTaiUserSelect\n                                }, void 0, false, void 0, void 0)\n                            }\n                        ],\n                        bottomBar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_bottom_bar__WEBPACK_IMPORTED_MODULE_2__.BottomBar, {\n                            mode: mode,\n                            onAdd: onAddButtonClick,\n                            onEdit: onEditButtonClick,\n                            onDelete: onDeleteButtonClick,\n                            onCopy: onCopyButtonClick,\n                            onClose: onClose\n                        }, void 0, false, void 0, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-4 mb-4 rounded bg-red-100 p-2 text-red-700\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 19\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConfirmDialog__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onClose: handleCloseDialog,\n                open: showConfirmDialog,\n                onCloseConfirmDialog: ()=>setShowConfirmDialog(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                lineNumber: 407,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FormDialog, \"4w9sOHT+l7xnmZvzgBdVxtcjF1c=\", false, function() {\n    return [\n        _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = FormDialog;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FormDialog);\nvar _c;\n$RefreshReg$(_c, \"FormDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx\n"));

/***/ })

});