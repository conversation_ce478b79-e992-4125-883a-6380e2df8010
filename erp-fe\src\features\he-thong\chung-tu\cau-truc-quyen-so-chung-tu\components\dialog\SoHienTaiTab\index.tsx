import React from 'react';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import InputTableActionBar from '../InputTableTab/InputTableActionBar';
import { getSoHienTaiColumns } from '../InputTableTab/cols';

import { GridCellParams } from '@mui/x-data-grid';
import { SelectedCellInfo } from '../InputTableTab/useInputTableRow';
import type { FormMode } from '@/types/form';

interface SoHienTaiTabProps {
  mode: FormMode;
  rows: any[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: (params: any) => void;
  onCellClick?: (params: GridCellParams) => void;
  onAddRow: () => void;
  onDeleteRow: () => void;
  onCopyRow: () => void;
  onPasteRow: () => void;
  onMoveRow: (direction: 'up' | 'down') => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
}

const SoHienTaiTab: React.FC<SoHienTaiTabProps> = ({
  mode,
  rows,
  selectedRowUuid,
  onRowClick,
  onAddRow,
  onDeleteRow,
  onCopyRow,
  onPasteRow,
  onMoveRow,
  onCellValueChange
}) => {
  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getSoHienTaiColumns({
        onCellValueChange
      })}
      getRowId={row => row?.uuid || ''}
      actionButtons={
        <InputTableActionBar
          mode={mode}
          handleAddRow={onAddRow}
          handleDeleteRow={onDeleteRow}
          handleCopyRow={onCopyRow}
          handlePasteRow={onPasteRow}
          handleMoveRow={onMoveRow}
        />
      }
    />
  );
};

export default SoHienTaiTab;
