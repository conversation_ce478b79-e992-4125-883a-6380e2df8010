import React from 'react';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import SoHienTaiActionBar from './SoHienTaiActionBar';
import { getSoHienTaiColumns } from '../InputTableTab/cols';

import { GridCellParams } from '@mui/x-data-grid';
import { SelectedCellInfo } from '../InputTableTab/useInputTableRow';
import type { FormMode } from '@/types/form';

interface SoHienTaiTabProps {
  mode: FormMode;
  rows: any[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: (params: any) => void;
  onCellClick?: (params: GridCellParams) => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
}

const SoHienTaiTab: React.FC<SoHienTaiTabProps> = ({
  mode,
  rows,
  selectedRowUuid,
  onRowClick,
  onCellValueChange
}) => {
  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getSoHienTaiColumns({
        onCellValueChange
      })}
      getRowId={row => row?.uuid || ''}
      actionButtons={
        <SoHienTaiActionBar
          mode={mode}
        />
      }
    />
  );
};

export default SoHienTaiTab;
