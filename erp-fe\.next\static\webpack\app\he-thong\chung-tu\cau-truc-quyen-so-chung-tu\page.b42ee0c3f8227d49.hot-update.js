"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx":
/*!************************************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx ***!
  \************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getInputTableColumns: function() { return /* binding */ getInputTableColumns; },\n/* harmony export */   getSoHienTaiColumns: function() { return /* binding */ getSoHienTaiColumns; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/search-columns */ \"(app-pages-browser)/./src/constants/search-columns/index.tsx\");\n/* harmony import */ var _components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito/custom-input-table/components */ \"(app-pages-browser)/./src/components/custom/arito/custom-input-table/components/index.ts\");\n/* harmony import */ var _components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom/arito/form/custom-search-field */ \"(app-pages-browser)/./src/components/custom/arito/form/custom-search-field/index.tsx\");\n/* harmony import */ var _constants_query_keys__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/query-keys */ \"(app-pages-browser)/./src/constants/query-keys.ts\");\n\n\n\n\n\n\nconst getInputTableColumns = (param)=>{\n    let { onCellValueChange, onChungTuSelect, onUserSelect } = param;\n    return [\n        {\n            field: \"ma_ct\",\n            headerName: \"M\\xe3 chứng từ\",\n            width: 175,\n            renderCell: (params)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__.SearchField, {\n                    value: params.row.ma_ct || \"\",\n                    onValueChange: (newValue)=>{\n                        onCellValueChange(params.row.uuid, \"ma_ct\", newValue);\n                    },\n                    searchEndpoint: \"/\".concat(_constants_query_keys__WEBPACK_IMPORTED_MODULE_5__[\"default\"].CHUNG_TU),\n                    searchColumns: _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__.chungTuSearchColumns,\n                    dialogTitle: \"Danh mục chứng từ\",\n                    className: \"w-full\",\n                    displayRelatedField: \"ten_ct\",\n                    columnDisplay: \"ma_ct\",\n                    relatedFieldValue: params.row.ten_ct || \"\",\n                    onRowSelection: (selectedChungTu)=>{\n                        if (selectedChungTu) {\n                            onCellValueChange(params.row.uuid, \"ma_ct\", selectedChungTu.ma_ct);\n                            onCellValueChange(params.row.uuid, \"ten_ct\", selectedChungTu.ten_ct || \"\");\n                            if (onChungTuSelect) {\n                                onChungTuSelect(params.row.uuid, selectedChungTu);\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"ten_ct\",\n            headerName: \"T\\xean chứng từ\",\n            width: 200,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__.CellField, {\n                    name: \"ten_ct\",\n                    type: \"text\",\n                    value: params.row.ten_ct,\n                    onValueChange: (newValue)=>onCellValueChange(params.row.uuid, \"ten_ct\", newValue),\n                    disabled: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 7\n                }, undefined)\n        },\n        {\n            field: \"user_id\",\n            headerName: \"Người sử dụng Nh\\xf3m\",\n            width: 175,\n            renderCell: (params)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__.SearchField, {\n                    value: params.row.username || \"\",\n                    onValueChange: (newValue)=>{\n                        onCellValueChange(params.row.uuid, \"username\", newValue);\n                        // Khi người dùng xóa giá trị, cũng xóa related field\n                        if (!newValue) {\n                            onCellValueChange(params.row.uuid, \"first_name\", \"\");\n                        }\n                    },\n                    otherSearchEndpoint: \"/\".concat(_constants_query_keys__WEBPACK_IMPORTED_MODULE_5__[\"default\"].NGUOI_SU_DUNG),\n                    searchEndpoint: \"\",\n                    searchColumns: _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__.userSearchColumns,\n                    dialogTitle: \"Danh mục người sử dụng\",\n                    className: \"w-full\",\n                    displayRelatedField: \"first_name\",\n                    columnDisplay: \"username\",\n                    relatedFieldValue: params.row.first_name || \"\",\n                    onRowSelection: (selectedUser)=>{\n                        if (selectedUser) {\n                            onCellValueChange(params.row.uuid, \"username\", selectedUser.username);\n                            onCellValueChange(params.row.uuid, \"first_name\", selectedUser.first_name);\n                            // Call parent callback for additional processing\n                            if (onUserSelect) {\n                                onUserSelect(params.row.uuid, selectedUser);\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"username\",\n            headerName: \"T\\xean người sử dụng\",\n            width: 200,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__.CellField, {\n                    name: \"first_name\",\n                    type: \"text\",\n                    value: params.row.first_name,\n                    onValueChange: (newValue)=>onCellValueChange(params.row.uuid, \"first_name\", newValue),\n                    disabled: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 7\n                }, undefined)\n        }\n    ];\n};\nconst getSoHienTaiColumns = (param)=>{\n    let { onCellValueChange } = param;\n    return [\n        {\n            field: \"ngay\",\n            headerName: \"Ng\\xe0y\",\n            width: 200,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__.CellField, {\n                    name: \"ngay\",\n                    type: \"date\",\n                    value: params.row.ngay || \"\",\n                    onValueChange: (newValue)=>onCellValueChange(params.row.uuid, \"ngay\", newValue)\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 7\n                }, undefined)\n        },\n        {\n            field: \"so_hien_tai\",\n            headerName: \"Số hiện tại\",\n            width: 200,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__.CellField, {\n                    name: \"so_hien_tai\",\n                    type: \"number\",\n                    value: params.row.so_hien_tai || \"\",\n                    onValueChange: (newValue)=>onCellValueChange(params.row.uuid, \"so_hien_tai\", newValue)\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 7\n                }, undefined)\n        }\n    ];\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx\n"));

/***/ })

});