"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/SoHienTaiActionBar.tsx":
/*!*************************************************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/SoHienTaiActionBar.tsx ***!
  \*************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SoHienTaiActionBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton,Tooltip!=!@mui/material */ \"(app-pages-browser)/./node_modules/.pnpm/@mui+material@6.4.11_@emoti_fdb03c9cf50ce9da1a20b3d9c4752c0d/node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton,Tooltip!=!@mui/material */ \"(app-pages-browser)/./node_modules/.pnpm/@mui+material@6.4.11_@emoti_fdb03c9cf50ce9da1a20b3d9c4752c0d/node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _components_custom_arito_custom_input_table_components_action_button_set__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/custom/arito/custom-input-table/components/action-button-set */ \"(app-pages-browser)/./src/components/custom/arito/custom-input-table/components/action-button-set/index.tsx\");\n/* harmony import */ var _components_custom_arito_icon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/icon */ \"(app-pages-browser)/./src/components/custom/arito/icon/index.tsx\");\n\n\n\n\nconst renderActionButton = (button)=>{\n    let icon;\n    let title;\n    if (button.icon !== undefined && button.title) {\n        icon = button.icon;\n        title = button.title;\n    } else {\n        const config = _components_custom_arito_custom_input_table_components_action_button_set__WEBPACK_IMPORTED_MODULE_1__.buttonConfigs[button.type] || {\n            icon: 0,\n            title: \"\"\n        };\n        icon = config.icon;\n        title = config.title;\n    }\n    const isDisabled = button.disabled || false;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: title,\n        arrow: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"small\",\n                onClick: button.onClick,\n                disabled: isDisabled,\n                sx: _components_custom_arito_custom_input_table_components_action_button_set__WEBPACK_IMPORTED_MODULE_1__.buttonSx,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_icon__WEBPACK_IMPORTED_MODULE_2__.AritoIcon, {\n                    icon: icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\nfunction SoHienTaiActionBar(param) {\n    let { mode } = param;\n    // Only show the \"pin\" (Cố định cột) button\n    const buttonTypes = [\n        \"pin\"\n    ];\n    const buttonActions = {\n        pin: ()=>console.log(\"Pin clicked\")\n    };\n    const buttons = buttonTypes.map((type)=>({\n            type,\n            onClick: buttonActions[type],\n            disabled: false\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-9 flex-none items-center justify-between border-b border-gray-200 bg-white px-1 py-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-full flex-1 items-center justify-between\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: buttons.map((button, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: renderActionButton(button)\n                    }, button.type + index, false, {\n                        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_c = SoHienTaiActionBar;\nvar _c;\n$RefreshReg$(_c, \"SoHienTaiActionBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/SoHienTaiActionBar.tsx\n"));

/***/ })

});