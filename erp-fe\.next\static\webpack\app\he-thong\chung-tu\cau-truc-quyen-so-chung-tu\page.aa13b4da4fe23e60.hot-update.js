"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx":
/*!***********************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx ***!
  \***********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_custom_arito_form_bottom_bar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/form/bottom-bar */ \"(app-pages-browser)/./src/components/custom/arito/form/bottom-bar.tsx\");\n/* harmony import */ var _components_custom_arito__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito */ \"(app-pages-browser)/./src/components/custom/arito/index.ts\");\n/* harmony import */ var _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./InputTableTab/useInputTableRow */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/useInputTableRow.ts\");\n/* harmony import */ var _components_custom_arito_icon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/custom/arito/icon */ \"(app-pages-browser)/./src/components/custom/arito/icon/index.tsx\");\n/* harmony import */ var _ConfirmDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ConfirmDialog */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/ConfirmDialog.tsx\");\n/* harmony import */ var _InputTableTab__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./InputTableTab */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/index.tsx\");\n/* harmony import */ var _SoHienTaiTab__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./SoHienTaiTab */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/index.tsx\");\n/* harmony import */ var _schemas__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../schemas */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/schemas.ts\");\n/* harmony import */ var _GeneralTab__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./GeneralTab */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/GeneralTab.tsx\");\n/* harmony import */ var _BasicForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./BasicForm */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/BasicForm.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction FormDialog(param) {\n    let { mode, open, onClose, onAddButtonClick, onCopyButtonClick, onDeleteButtonClick, onEditButtonClick, onSubmit, initialData } = param;\n    _s();\n    const [showConfirmDialog, setShowConfirmDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [donVi, setDonVi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Hàm tạo dòng mới tùy chỉnh cho bảng \"Chứng từ & Người sử dụng\"\n    const createGiaBanRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newRow = {\n            uuid: String(Math.random()),\n            ma_ct: \"\",\n            ten_ct: \"\",\n            user_id: \"\",\n            username: \"\",\n            first_name: \"\" // Tên người sử dụng để hiển thị\n        };\n        return newRow;\n    }, []);\n    // Hàm tạo dòng mới tùy chỉnh cho bảng \"Số hiện tại\"\n    const createSoHienTaiRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newRow = {\n            uuid: String(Math.random()),\n            ngay: \"\",\n            so_hien_tai: \"\" // Số hiện tại\n        };\n        return newRow;\n    }, []);\n    // Sử dụng hook useInputTableRow cho tab \"Chứng từ & Người sử dụng\"\n    const { rows: tableData, setRows, handleRowClick, handleAddRow: originalHandleAddRow, handleDeleteRow, handleCopyRow, handlePasteRow, handleMoveRow, handleCellValueChange: originalHandleCellValueChange } = (0,_InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([], createGiaBanRow);\n    const { rows: soHienTaiTableData, setRows: setSoHienTaiRows, handleRowClick: handleSoHienTaiRowClick, handleAddRow: originalHandleSoHienTaiAddRow, handleDeleteRow: handleSoHienTaiDeleteRow, handleCopyRow: handleSoHienTaiCopyRow, handlePasteRow: handleSoHienTaiPasteRow, handleMoveRow: handleSoHienTaiMoveRow, handleCellValueChange: originalHandleSoHienTaiCellValueChange } = (0,_InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([], createSoHienTaiRow);\n    // Wrap handleAddRow to add logging\n    const handleAddRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        originalHandleAddRow();\n    }, [\n        originalHandleAddRow\n    ]);\n    // Wrap handleCellValueChange to add logging\n    const handleCellValueChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, field, newValue)=>{\n        originalHandleCellValueChange(rowUuid, field, newValue);\n    }, [\n        originalHandleCellValueChange\n    ]);\n    // Wrap handlers for \"Số hiện tại\" tab\n    const handleSoHienTaiAddRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        originalHandleSoHienTaiAddRow();\n    }, [\n        originalHandleSoHienTaiAddRow\n    ]);\n    const handleSoHienTaiCellValueChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, field, newValue)=>{\n        originalHandleSoHienTaiCellValueChange(rowUuid, field, newValue);\n    }, [\n        originalHandleSoHienTaiCellValueChange\n    ]);\n    // State for storing selected data following GeneralTab pattern\n    const [selectedChungTuData, setSelectedChungTuData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedUserData, setSelectedUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Callback handlers following GeneralTab pattern\n    const handleChungTuSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, chungTu)=>{\n        setSelectedChungTuData((prev)=>{\n            const updated = {\n                ...prev,\n                [rowUuid]: chungTu\n            };\n            return updated;\n        });\n    }, []);\n    const handleUserSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, user)=>{\n        setSelectedUserData((prev)=>{\n            const updated = {\n                ...prev,\n                [rowUuid]: user\n            };\n            return updated;\n        });\n    }, []);\n    const handleSubmit = async (data)=>{\n        console.log(\"Form data>>>>>>>>>>>:\", data);\n        console.log(\"Table data>>>>>>>>>>>:\", tableData);\n        // Create combined list with ma_ct (UUID) and username format for \"Chứng từ & Người sử dụng\" tab\n        const combinedList = tableData.map((row)=>{\n            const chungTu = selectedChungTuData[row.uuid];\n            const user = selectedUserData[row.uuid];\n            return {\n                ma_ct: (chungTu === null || chungTu === void 0 ? void 0 : chungTu.uuid) || \"\",\n                username: (user === null || user === void 0 ? void 0 : user.username) || row.username || \"\"\n            };\n        });\n        // Create combined list for \"Số hiện tại\" tab\n        const soHienTaiCombinedList = soHienTaiTableData.map((row)=>{\n            return {\n                ngay: row.ngay || \"\",\n                so_hien_tai: row.so_hien_tai || \"\"\n            };\n        });\n        // Log the combined lists in requested format\n        console.log(\"\\uD83D\\uDCCB\\uD83D\\uDC65 List of ChungTu and User:\", combinedList);\n        try {\n            if (onSubmit) {\n                const formData = {\n                    ...data,\n                    danh_sach_chung_tu: combinedList,\n                    danh_sach_so_hien_tai: soHienTaiCombinedList\n                };\n                await onSubmit(formData);\n            } else {\n                setError(\"Kh\\xf4ng thể lưu dữ liệu: Kh\\xf4ng c\\xf3 xử l\\xfd submit\");\n            }\n        } catch (err) {\n            setError(err.message || \"C\\xf3 lỗi xảy ra\");\n        }\n    };\n    const handleCloseDialog = ()=>{\n        setShowConfirmDialog(false);\n        onClose();\n    };\n    // Effect to update form fields when initialData changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialData) {\n            // Process initialData.danh_sach_chung_tu if available\n            if (initialData.danh_sach_chung_tu && Array.isArray(initialData.danh_sach_chung_tu) && initialData.danh_sach_chung_tu.length > 0) {\n                // Convert danh_sach_chung_tu array to the format expected by the table\n                const tableRows = initialData.danh_sach_chung_tu.map((item)=>{\n                    // Create a new row with a unique UUID\n                    const newRow = createGiaBanRow();\n                    // Set the values from the danh_sach_chung_tu item\n                    newRow.ma_ct = item.ma_ct || \"\";\n                    newRow.ten_ct = item.ten_ct || \"\";\n                    newRow.user_id = item.id || \"\";\n                    newRow.username = item.username || \"\";\n                    newRow.first_name = item.first_name || \"\";\n                    // Store the selected data for form submission (following GeneralTab pattern)\n                    if (item.ma_ct) {\n                        setSelectedChungTuData((prev)=>({\n                                ...prev,\n                                [newRow.uuid]: item // Store the full item data\n                            }));\n                    }\n                    if (item.username) {\n                        setSelectedUserData((prev)=>({\n                                ...prev,\n                                [newRow.uuid]: item // Store the full item data\n                            }));\n                    }\n                    return newRow;\n                });\n                // Set the table data\n                if (tableRows.length > 0) {\n                    // Use the setRows function from useInputTableRow\n                    setRows(tableRows);\n                }\n            }\n            // Process initialData.danh_sach_so_hien_tai if available\n            if (initialData.danh_sach_so_hien_tai && Array.isArray(initialData.danh_sach_so_hien_tai) && initialData.danh_sach_so_hien_tai.length > 0) {\n                // Convert danh_sach_so_hien_tai array to the format expected by the table\n                const soHienTaiTableRows = initialData.danh_sach_so_hien_tai.map((item)=>{\n                    // Create a new row with a unique UUID\n                    const newRow = createSoHienTaiRow();\n                    // Set the values from the danh_sach_so_hien_tai item\n                    newRow.ngay = item.ngay || \"\";\n                    newRow.so_hien_tai = item.so_hien_tai || \"\";\n                    return newRow;\n                });\n                // Set the table data for \"Số hiện tại\" tab\n                if (soHienTaiTableRows.length > 0) {\n                    setSoHienTaiRows(soHienTaiTableRows);\n                }\n            }\n        }\n    }, [\n        initialData,\n        setRows,\n        setSoHienTaiRows,\n        createGiaBanRow,\n        createSoHienTaiRow\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_3__.AritoDialog, {\n                open: open,\n                onClose: onClose,\n                title: mode === \"add\" ? \"Mới\" : mode === \"edit\" ? \"Sửa\" : \"Xem\",\n                maxWidth: \"lg\",\n                disableBackdropClose: false,\n                disableEscapeKeyDown: false,\n                titleIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_icon__WEBPACK_IMPORTED_MODULE_5__.AritoIcon, {\n                    icon: 281\n                }, void 0, false, void 0, void 0),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_3__.AritoForm, {\n                        mode: mode,\n                        hasAritoActionBar: false,\n                        schema: _schemas__WEBPACK_IMPORTED_MODULE_9__.formSchema,\n                        onSubmit: (data)=>{\n                            handleSubmit(data);\n                        },\n                        initialData: initialData,\n                        className: \"w-[50vw]\",\n                        headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BasicForm__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            mode: mode\n                        }, void 0, false, void 0, void 0),\n                        classNameBottomBar: \"relative w-full flex justify-end gap-2\",\n                        tabs: [\n                            {\n                                id: \"1\",\n                                label: \"Th\\xf4ng tin chung\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GeneralTab__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    mode: mode,\n                                    donVi: donVi,\n                                    setDonVi: setDonVi\n                                }, void 0, false, void 0, void 0)\n                            },\n                            {\n                                id: \"3\",\n                                label: \"Chứng từ & Người sử dụng\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputTableTab__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    mode: mode,\n                                    rows: tableData,\n                                    onRowClick: handleRowClick,\n                                    onAddRow: handleAddRow,\n                                    onDeleteRow: handleDeleteRow,\n                                    onCopyRow: handleCopyRow,\n                                    onPasteRow: handlePasteRow,\n                                    onMoveRow: handleMoveRow,\n                                    onCellValueChange: handleCellValueChange,\n                                    onChungTuSelect: handleChungTuSelect,\n                                    onUserSelect: handleUserSelect\n                                }, void 0, false, void 0, void 0)\n                            },\n                            {\n                                id: \"4\",\n                                label: \"Số hiện tại\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SoHienTaiTab__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    mode: mode,\n                                    rows: soHienTaiTableData,\n                                    onRowClick: handleSoHienTaiRowClick,\n                                    onCellValueChange: handleSoHienTaiCellValueChange\n                                }, void 0, false, void 0, void 0)\n                            }\n                        ],\n                        bottomBar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_bottom_bar__WEBPACK_IMPORTED_MODULE_2__.BottomBar, {\n                            mode: mode,\n                            onAdd: onAddButtonClick,\n                            onEdit: onEditButtonClick,\n                            onDelete: onDeleteButtonClick,\n                            onCopy: onCopyButtonClick,\n                            onClose: onClose\n                        }, void 0, false, void 0, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-4 mb-4 rounded bg-red-100 p-2 text-red-700\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 19\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConfirmDialog__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onClose: handleCloseDialog,\n                open: showConfirmDialog,\n                onCloseConfirmDialog: ()=>setShowConfirmDialog(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FormDialog, \"kfs092pI0gvQiQqgh67eXLyYrpQ=\", false, function() {\n    return [\n        _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = FormDialog;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FormDialog);\nvar _c;\n$RefreshReg$(_c, \"FormDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9oZS10aG9uZy9jaHVuZy10dS9jYXUtdHJ1Yy1xdXllbi1zby1jaHVuZy10dS9jb21wb25lbnRzL2RpYWxvZy9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUF5RDtBQUVhO0FBQ0g7QUFDSDtBQUNMO0FBRWQ7QUFDRDtBQUNGO0FBQ0M7QUFFTDtBQUNGO0FBNEJwQyxTQUFTYyxXQUFXLEtBVUY7UUFWRSxFQUNsQkMsSUFBSSxFQUNKQyxJQUFJLEVBQ0pDLE9BQU8sRUFDUEMsZ0JBQWdCLEVBQ2hCQyxpQkFBaUIsRUFDakJDLG1CQUFtQixFQUNuQkMsaUJBQWlCLEVBQ2pCQyxRQUFRLEVBQ1JDLFdBQVcsRUFDSyxHQVZFOztJQVdsQixNQUFNLENBQUNDLG1CQUFtQkMscUJBQXFCLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUMwQixPQUFPQyxTQUFTLEdBQUczQiwrQ0FBUUEsQ0FBZ0I7SUFFbEQsTUFBTSxDQUFDNEIsT0FBT0MsU0FBUyxHQUFHN0IsK0NBQVFBLENBQW1CO0lBRXJELGlFQUFpRTtJQUNqRSxNQUFNOEIsa0JBQWtCNUIsa0RBQVdBLENBQUM7UUFDbEMsTUFBTTZCLFNBQVM7WUFDYkMsTUFBTUMsT0FBT0MsS0FBS0MsTUFBTTtZQUN4QkMsT0FBTztZQUNQQyxRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsVUFBVTtZQUNWQyxZQUFZLEdBQUcsZ0NBQWdDO1FBQ2pEO1FBQ0EsT0FBT1Q7SUFDVCxHQUFHLEVBQUU7SUFFTCxvREFBb0Q7SUFDcEQsTUFBTVUscUJBQXFCdkMsa0RBQVdBLENBQUM7UUFDckMsTUFBTTZCLFNBQVM7WUFDYkMsTUFBTUMsT0FBT0MsS0FBS0MsTUFBTTtZQUN4Qk8sTUFBTTtZQUNOQyxhQUFhLEdBQUcsY0FBYztRQUNoQztRQUNBLE9BQU9aO0lBQ1QsR0FBRyxFQUFFO0lBRUwsbUVBQW1FO0lBQ25FLE1BQU0sRUFDSmEsTUFBTUMsU0FBUyxFQUNmQyxPQUFPLEVBQ1BDLGNBQWMsRUFDZEMsY0FBY0Msb0JBQW9CLEVBQ2xDQyxlQUFlLEVBQ2ZDLGFBQWEsRUFDYkMsY0FBYyxFQUNkQyxhQUFhLEVBQ2JDLHVCQUF1QkMsNkJBQTZCLEVBQ3JELEdBQUdqRCwyRUFBZ0JBLENBQU0sRUFBRSxFQUFFd0I7SUFFOUIsTUFBTSxFQUNKYyxNQUFNWSxrQkFBa0IsRUFDeEJWLFNBQVNXLGdCQUFnQixFQUN6QlYsZ0JBQWdCVyx1QkFBdUIsRUFDdkNWLGNBQWNXLDZCQUE2QixFQUMzQ1QsaUJBQWlCVSx3QkFBd0IsRUFDekNULGVBQWVVLHNCQUFzQixFQUNyQ1QsZ0JBQWdCVSx1QkFBdUIsRUFDdkNULGVBQWVVLHNCQUFzQixFQUNyQ1QsdUJBQXVCVSxzQ0FBc0MsRUFDOUQsR0FBRzFELDJFQUFnQkEsQ0FBTSxFQUFFLEVBQUVtQztJQUU5QixtQ0FBbUM7SUFDbkMsTUFBTU8sZUFBZTlDLGtEQUFXQSxDQUFDO1FBQy9CK0M7SUFDRixHQUFHO1FBQUNBO0tBQXFCO0lBRXpCLDRDQUE0QztJQUM1QyxNQUFNSyx3QkFBd0JwRCxrREFBV0EsQ0FDdkMsQ0FBQytELFNBQWlCQyxPQUFlQztRQUMvQlosOEJBQThCVSxTQUFTQyxPQUFPQztJQUNoRCxHQUNBO1FBQUNaO0tBQThCO0lBR2pDLHNDQUFzQztJQUN0QyxNQUFNYSx3QkFBd0JsRSxrREFBV0EsQ0FBQztRQUN4Q3lEO0lBQ0YsR0FBRztRQUFDQTtLQUE4QjtJQUVsQyxNQUFNVSxpQ0FBaUNuRSxrREFBV0EsQ0FDaEQsQ0FBQytELFNBQWlCQyxPQUFlQztRQUMvQkgsdUNBQXVDQyxTQUFTQyxPQUFPQztJQUN6RCxHQUNBO1FBQUNIO0tBQXVDO0lBRzFDLCtEQUErRDtJQUMvRCxNQUFNLENBQUNNLHFCQUFxQkMsdUJBQXVCLEdBQUd2RSwrQ0FBUUEsQ0FBMEIsQ0FBQztJQUN6RixNQUFNLENBQUN3RSxrQkFBa0JDLG9CQUFvQixHQUFHekUsK0NBQVFBLENBQXVCLENBQUM7SUFFaEYsaURBQWlEO0lBQ2pELE1BQU0wRSxzQkFBc0J4RSxrREFBV0EsQ0FBQyxDQUFDK0QsU0FBaUJVO1FBQ3hESix1QkFBdUJLLENBQUFBO1lBQ3JCLE1BQU1DLFVBQVU7Z0JBQ2QsR0FBR0QsSUFBSTtnQkFDUCxDQUFDWCxRQUFRLEVBQUVVO1lBQ2I7WUFDQSxPQUFPRTtRQUNUO0lBQ0YsR0FBRyxFQUFFO0lBRUwsTUFBTUMsbUJBQW1CNUUsa0RBQVdBLENBQUMsQ0FBQytELFNBQWlCYztRQUNyRE4sb0JBQW9CRyxDQUFBQTtZQUNsQixNQUFNQyxVQUFVO2dCQUNkLEdBQUdELElBQUk7Z0JBQ1AsQ0FBQ1gsUUFBUSxFQUFFYztZQUNiO1lBRUEsT0FBT0Y7UUFDVDtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1HLGVBQWUsT0FBT0M7UUFDMUJDLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUJGO1FBQ3JDQyxRQUFRQyxHQUFHLENBQUMsMEJBQTBCdEM7UUFFdEMsZ0dBQWdHO1FBQ2hHLE1BQU11QyxlQUFldkMsVUFBVXdDLEdBQUcsQ0FBQ0MsQ0FBQUE7WUFDakMsTUFBTVgsVUFBVUwsbUJBQW1CLENBQUNnQixJQUFJdEQsSUFBSSxDQUFDO1lBQzdDLE1BQU0rQyxPQUFPUCxnQkFBZ0IsQ0FBQ2MsSUFBSXRELElBQUksQ0FBQztZQUV2QyxPQUFPO2dCQUNMSSxPQUFPdUMsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTM0MsSUFBSSxLQUFJO2dCQUN4Qk8sVUFBVXdDLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXhDLFFBQVEsS0FBSStDLElBQUkvQyxRQUFRLElBQUk7WUFDOUM7UUFDRjtRQUVBLDZDQUE2QztRQUM3QyxNQUFNZ0Qsd0JBQXdCL0IsbUJBQW1CNkIsR0FBRyxDQUFDQyxDQUFBQTtZQUNuRCxPQUFPO2dCQUNMNUMsTUFBTTRDLElBQUk1QyxJQUFJLElBQUk7Z0JBQ2xCQyxhQUFhMkMsSUFBSTNDLFdBQVcsSUFBSTtZQUNsQztRQUNGO1FBRUEsNkNBQTZDO1FBQzdDdUMsUUFBUUMsR0FBRyxDQUFDLHNEQUFrQ0M7UUFFOUMsSUFBSTtZQUNGLElBQUk5RCxVQUFVO2dCQUNaLE1BQU1rRSxXQUFXO29CQUNmLEdBQUdQLElBQUk7b0JBQ1BRLG9CQUFvQkw7b0JBQ3BCTSx1QkFBdUJIO2dCQUN6QjtnQkFDQSxNQUFNakUsU0FBU2tFO1lBQ2pCLE9BQU87Z0JBQ0w3RCxTQUFTO1lBQ1g7UUFDRixFQUFFLE9BQU9nRSxLQUFVO1lBQ2pCaEUsU0FBU2dFLElBQUlDLE9BQU8sSUFBSTtRQUMxQjtJQUNGO0lBRUEsTUFBTUMsb0JBQW9CO1FBQ3hCcEUscUJBQXFCO1FBQ3JCUjtJQUNGO0lBRUEsd0RBQXdEO0lBQ3hEaEIsZ0RBQVNBLENBQUM7UUFDUixJQUFJc0IsYUFBYTtZQUNmLHNEQUFzRDtZQUN0RCxJQUNFQSxZQUFZa0Usa0JBQWtCLElBQzlCSyxNQUFNQyxPQUFPLENBQUN4RSxZQUFZa0Usa0JBQWtCLEtBQzVDbEUsWUFBWWtFLGtCQUFrQixDQUFDTyxNQUFNLEdBQUcsR0FDeEM7Z0JBQ0EsdUVBQXVFO2dCQUN2RSxNQUFNQyxZQUFZMUUsWUFBWWtFLGtCQUFrQixDQUFDSixHQUFHLENBQUMsQ0FBQ2E7b0JBQ3BELHNDQUFzQztvQkFDdEMsTUFBTW5FLFNBQVNEO29CQUVmLGtEQUFrRDtvQkFDbERDLE9BQU9LLEtBQUssR0FBRzhELEtBQUs5RCxLQUFLLElBQUk7b0JBQzdCTCxPQUFPTSxNQUFNLEdBQUc2RCxLQUFLN0QsTUFBTSxJQUFJO29CQUMvQk4sT0FBT08sT0FBTyxHQUFHNEQsS0FBS0MsRUFBRSxJQUFJO29CQUM1QnBFLE9BQU9RLFFBQVEsR0FBRzJELEtBQUszRCxRQUFRLElBQUk7b0JBQ25DUixPQUFPUyxVQUFVLEdBQUcwRCxLQUFLMUQsVUFBVSxJQUFJO29CQUV2Qyw2RUFBNkU7b0JBQzdFLElBQUkwRCxLQUFLOUQsS0FBSyxFQUFFO3dCQUNkbUMsdUJBQXVCSyxDQUFBQSxPQUFTO2dDQUM5QixHQUFHQSxJQUFJO2dDQUNQLENBQUM3QyxPQUFPQyxJQUFJLENBQUMsRUFBRWtFLEtBQUssMkJBQTJCOzRCQUNqRDtvQkFDRjtvQkFFQSxJQUFJQSxLQUFLM0QsUUFBUSxFQUFFO3dCQUNqQmtDLG9CQUFvQkcsQ0FBQUEsT0FBUztnQ0FDM0IsR0FBR0EsSUFBSTtnQ0FDUCxDQUFDN0MsT0FBT0MsSUFBSSxDQUFDLEVBQUVrRSxLQUFLLDJCQUEyQjs0QkFDakQ7b0JBQ0Y7b0JBRUEsT0FBT25FO2dCQUNUO2dCQUVBLHFCQUFxQjtnQkFDckIsSUFBSWtFLFVBQVVELE1BQU0sR0FBRyxHQUFHO29CQUN4QixpREFBaUQ7b0JBQ2pEbEQsUUFBUW1EO2dCQUNWO1lBQ0Y7WUFFQSx5REFBeUQ7WUFDekQsSUFDRTFFLFlBQVltRSxxQkFBcUIsSUFDakNJLE1BQU1DLE9BQU8sQ0FBQ3hFLFlBQVltRSxxQkFBcUIsS0FDL0NuRSxZQUFZbUUscUJBQXFCLENBQUNNLE1BQU0sR0FBRyxHQUMzQztnQkFDQSwwRUFBMEU7Z0JBQzFFLE1BQU1JLHFCQUFxQjdFLFlBQVltRSxxQkFBcUIsQ0FBQ0wsR0FBRyxDQUFDLENBQUNhO29CQUNoRSxzQ0FBc0M7b0JBQ3RDLE1BQU1uRSxTQUFTVTtvQkFFZixxREFBcUQ7b0JBQ3JEVixPQUFPVyxJQUFJLEdBQUd3RCxLQUFLeEQsSUFBSSxJQUFJO29CQUMzQlgsT0FBT1ksV0FBVyxHQUFHdUQsS0FBS3ZELFdBQVcsSUFBSTtvQkFFekMsT0FBT1o7Z0JBQ1Q7Z0JBRUEsMkNBQTJDO2dCQUMzQyxJQUFJcUUsbUJBQW1CSixNQUFNLEdBQUcsR0FBRztvQkFDakN2QyxpQkFBaUIyQztnQkFDbkI7WUFDRjtRQUNGO0lBQ0YsR0FBRztRQUFDN0U7UUFBYXVCO1FBQVNXO1FBQWtCM0I7UUFBaUJXO0tBQW1CO0lBRWhGLHFCQUNFOzswQkFDRSw4REFBQ3JDLGlFQUFXQTtnQkFDVlksTUFBTUE7Z0JBQ05DLFNBQVNBO2dCQUNUb0YsT0FBT3RGLFNBQVMsUUFBUSxRQUFRQSxTQUFTLFNBQVMsUUFBUTtnQkFDMUR1RixVQUFTO2dCQUNUQyxzQkFBc0I7Z0JBQ3RCQyxzQkFBc0I7Z0JBQ3RCQyx5QkFBVyw4REFBQ2xHLG9FQUFTQTtvQkFBQ21HLE1BQU07OztrQ0FFNUIsOERBQUNyRywrREFBU0E7d0JBQ1JVLE1BQU1BO3dCQUNONEYsbUJBQW1CO3dCQUNuQkMsUUFBUWpHLGdEQUFVQTt3QkFDbEJXLFVBQVUyRCxDQUFBQTs0QkFDUkQsYUFBYUM7d0JBQ2Y7d0JBQ0ExRCxhQUFhQTt3QkFDYnNGLFdBQVU7d0JBQ1ZDLDRCQUFjLDhEQUFDakcsbURBQVNBOzRCQUFDRSxNQUFNQTs7d0JBQy9CZ0csb0JBQW1CO3dCQUNuQkMsTUFBTTs0QkFDSjtnQ0FDRWIsSUFBSTtnQ0FDSmMsT0FBTztnQ0FDUEMseUJBQVcsOERBQUN0RyxvREFBVUE7b0NBQUNHLE1BQU1BO29DQUFNYSxPQUFPQTtvQ0FBT0MsVUFBVUE7OzRCQUM3RDs0QkFDQTtnQ0FDRXNFLElBQUk7Z0NBQ0pjLE9BQU87Z0NBQ1BDLHlCQUNFLDhEQUFDekcsc0RBQWFBO29DQUNaTSxNQUFNQTtvQ0FDTjZCLE1BQU1DO29DQUNOc0UsWUFBWXBFO29DQUNacUUsVUFBVXBFO29DQUNWcUUsYUFBYW5FO29DQUNib0UsV0FBV25FO29DQUNYb0UsWUFBWW5FO29DQUNab0UsV0FBV25FO29DQUNYb0UsbUJBQW1CbkU7b0NBQ25Cb0UsaUJBQWlCaEQ7b0NBQ2pCaUQsY0FBYzdDOzs0QkFHcEI7NEJBQ0E7Z0NBQ0VxQixJQUFJO2dDQUNKYyxPQUFPO2dDQUNQQyx5QkFDRSw4REFBQ3hHLHFEQUFZQTtvQ0FDWEssTUFBTUE7b0NBQ042QixNQUFNWTtvQ0FDTjJELFlBQVl6RDtvQ0FDWitELG1CQUFtQnBEOzs0QkFHekI7eUJBQ0Q7d0JBQ0R1RCx5QkFDRSw4REFBQ3pILCtFQUFTQTs0QkFDUlksTUFBTUE7NEJBQ044RyxPQUFPM0c7NEJBQ1A0RyxRQUFRekc7NEJBQ1IwRyxVQUFVM0c7NEJBQ1Y0RyxRQUFRN0c7NEJBQ1JGLFNBQVNBOzs7Ozs7O29CQUlkUyx1QkFBUyw4REFBQ3VHO3dCQUFJcEIsV0FBVTtrQ0FBaURuRjs7Ozs7Ozs7Ozs7OzBCQUc1RSw4REFBQ2xCLHNEQUFhQTtnQkFDWlMsU0FBUzRFO2dCQUNUN0UsTUFBTVE7Z0JBQ04wRyxzQkFBc0IsSUFBTXpHLHFCQUFxQjs7Ozs7Ozs7QUFJekQ7R0EzVFNYOztRQWtESFIsdUVBQWdCQTtRQVloQkEsdUVBQWdCQTs7O0tBOURiUTtBQTZUVCwrREFBZUEsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvZmVhdHVyZXMvaGUtdGhvbmcvY2h1bmctdHUvY2F1LXRydWMtcXV5ZW4tc28tY2h1bmctdHUvY29tcG9uZW50cy9kaWFsb2cvaW5kZXgudHN4P2M2MmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0BtdWkvbWF0ZXJpYWwnO1xyXG5pbXBvcnQgeyBCb3R0b21CYXIgfSBmcm9tICdAL2NvbXBvbmVudHMvY3VzdG9tL2FyaXRvL2Zvcm0vYm90dG9tLWJhcic7XHJcbmltcG9ydCB7IEFyaXRvRGlhbG9nLCBBcml0b0Zvcm0gfSBmcm9tICdAL2NvbXBvbmVudHMvY3VzdG9tL2FyaXRvJztcclxuaW1wb3J0IHVzZUlucHV0VGFibGVSb3cgZnJvbSAnLi9JbnB1dFRhYmxlVGFiL3VzZUlucHV0VGFibGVSb3cnO1xyXG5pbXBvcnQgeyBBcml0b0ljb24gfSBmcm9tICdAL2NvbXBvbmVudHMvY3VzdG9tL2FyaXRvL2ljb24nO1xyXG5pbXBvcnQgeyBEb25WaUNvU28sIENodW5nVHUgfSBmcm9tICdAL3R5cGVzL3NjaGVtYXMnO1xyXG5pbXBvcnQgQ29uZmlybURpYWxvZyBmcm9tICcuLi9Db25maXJtRGlhbG9nJztcclxuaW1wb3J0IElucHV0VGFibGVUYWIgZnJvbSAnLi9JbnB1dFRhYmxlVGFiJztcclxuaW1wb3J0IFNvSGllblRhaVRhYiBmcm9tICcuL1NvSGllblRhaVRhYic7XHJcbmltcG9ydCB7IGZvcm1TY2hlbWEgfSBmcm9tICcuLi8uLi9zY2hlbWFzJztcclxuaW1wb3J0IHsgRm9ybU1vZGUgfSBmcm9tICdAL3R5cGVzL2Zvcm0nO1xyXG5pbXBvcnQgR2VuZXJhbFRhYiBmcm9tICcuL0dlbmVyYWxUYWInO1xyXG5pbXBvcnQgQmFzaWNGb3JtIGZyb20gJy4vQmFzaWNGb3JtJztcclxuXHJcbi8vIEludGVyZmFjZSBmb3IgVXNlciBkYXRhIChtYXRjaGluZyBJbnB1dFRhYmxlVGFiKVxyXG5pbnRlcmZhY2UgVXNlciB7XHJcbiAgaWQ6IHN0cmluZztcclxuICB1dWlkOiBzdHJpbmc7XHJcbiAgdXNlcm5hbWU6IHN0cmluZztcclxuICBmaXJzdF9uYW1lOiBzdHJpbmc7XHJcbiAgZW1haWw/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmludGVyZmFjZSBGb3JtRGlhbG9nUHJvcHMge1xyXG4gIG1vZGU6IEZvcm1Nb2RlO1xyXG4gIG9wZW46IGJvb2xlYW47XHJcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcclxuXHJcbiAgb25BZGRCdXR0b25DbGljaz86ICgpID0+IHZvaWQ7XHJcbiAgb25FZGl0QnV0dG9uQ2xpY2s/OiAoKSA9PiB2b2lkO1xyXG4gIG9uRGVsZXRlQnV0dG9uQ2xpY2s/OiAoKSA9PiB2b2lkO1xyXG4gIG9uQ29weUJ1dHRvbkNsaWNrPzogKCkgPT4gdm9pZDtcclxuICBvbldhdGNoQnV0dG9uQ2xpY2s/OiAoKSA9PiB2b2lkO1xyXG5cclxuICAvLyBQYXJlbnQgY29tcG9uZW50J3Mgc3VibWl0IGhhbmRsZXIgLSB1cGRhdGVkIHRvIHVzZSBuZXcgZGF0YSBzdHJ1Y3R1cmVcclxuICBvblN1Ym1pdD86IChkYXRhOiBhbnkpID0+IHZvaWQ7XHJcblxyXG4gIGluaXRpYWxEYXRhPzogYW55O1xyXG59XHJcblxyXG5mdW5jdGlvbiBGb3JtRGlhbG9nKHtcclxuICBtb2RlLFxyXG4gIG9wZW4sXHJcbiAgb25DbG9zZSxcclxuICBvbkFkZEJ1dHRvbkNsaWNrLFxyXG4gIG9uQ29weUJ1dHRvbkNsaWNrLFxyXG4gIG9uRGVsZXRlQnV0dG9uQ2xpY2ssXHJcbiAgb25FZGl0QnV0dG9uQ2xpY2ssXHJcbiAgb25TdWJtaXQsXHJcbiAgaW5pdGlhbERhdGFcclxufTogRm9ybURpYWxvZ1Byb3BzKSB7XHJcbiAgY29uc3QgW3Nob3dDb25maXJtRGlhbG9nLCBzZXRTaG93Q29uZmlybURpYWxvZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuXHJcbiAgY29uc3QgW2RvblZpLCBzZXREb25WaV0gPSB1c2VTdGF0ZTxEb25WaUNvU28gfCBudWxsPihudWxsKTtcclxuXHJcbiAgLy8gSMOgbSB04bqhbyBkw7JuZyBt4bubaSB0w7l5IGNo4buJbmggY2hvIGLhuqNuZyBcIkNo4bupbmcgdOG7qyAmIE5nxrDhu51pIHPhu60gZOG7pW5nXCJcclxuICBjb25zdCBjcmVhdGVHaWFCYW5Sb3cgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBjb25zdCBuZXdSb3cgPSB7XHJcbiAgICAgIHV1aWQ6IFN0cmluZyhNYXRoLnJhbmRvbSgpKSxcclxuICAgICAgbWFfY3Q6ICcnLCAvLyBNw6MgY2jhu6luZyB04burIMSR4buDIGhp4buDbiB0aOG7i1xyXG4gICAgICB0ZW5fY3Q6ICcnLCAvLyBUw6puIGNo4bupbmcgdOG7qyDEkeG7gyBoaeG7g24gdGjhu4tcclxuICAgICAgdXNlcl9pZDogJycsIC8vIElEIG5nxrDhu51pIHPhu60gZOG7pW5nIChpbnRlcm5hbClcclxuICAgICAgdXNlcm5hbWU6ICcnLCAvLyBVc2VybmFtZSDEkeG7gyBoaeG7g24gdGjhu4tcclxuICAgICAgZmlyc3RfbmFtZTogJycgLy8gVMOqbiBuZ8aw4budaSBz4butIGThu6VuZyDEkeG7gyBoaeG7g24gdGjhu4tcclxuICAgIH07XHJcbiAgICByZXR1cm4gbmV3Um93O1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gSMOgbSB04bqhbyBkw7JuZyBt4bubaSB0w7l5IGNo4buJbmggY2hvIGLhuqNuZyBcIlPhu5EgaGnhu4duIHThuqFpXCJcclxuICBjb25zdCBjcmVhdGVTb0hpZW5UYWlSb3cgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBjb25zdCBuZXdSb3cgPSB7XHJcbiAgICAgIHV1aWQ6IFN0cmluZyhNYXRoLnJhbmRvbSgpKSxcclxuICAgICAgbmdheTogJycsIC8vIE5nw6B5XHJcbiAgICAgIHNvX2hpZW5fdGFpOiAnJyAvLyBT4buRIGhp4buHbiB04bqhaVxyXG4gICAgfTtcclxuICAgIHJldHVybiBuZXdSb3c7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBT4butIGThu6VuZyBob29rIHVzZUlucHV0VGFibGVSb3cgY2hvIHRhYiBcIkNo4bupbmcgdOG7qyAmIE5nxrDhu51pIHPhu60gZOG7pW5nXCJcclxuICBjb25zdCB7XHJcbiAgICByb3dzOiB0YWJsZURhdGEsXHJcbiAgICBzZXRSb3dzLCAvLyBVbmNvbW1lbnQgdG8gdXNlIGZvciBzZXR0aW5nIHRhYmxlIGRhdGEgZnJvbSBpbml0aWFsRGF0YVxyXG4gICAgaGFuZGxlUm93Q2xpY2ssXHJcbiAgICBoYW5kbGVBZGRSb3c6IG9yaWdpbmFsSGFuZGxlQWRkUm93LFxyXG4gICAgaGFuZGxlRGVsZXRlUm93LFxyXG4gICAgaGFuZGxlQ29weVJvdyxcclxuICAgIGhhbmRsZVBhc3RlUm93LFxyXG4gICAgaGFuZGxlTW92ZVJvdyxcclxuICAgIGhhbmRsZUNlbGxWYWx1ZUNoYW5nZTogb3JpZ2luYWxIYW5kbGVDZWxsVmFsdWVDaGFuZ2VcclxuICB9ID0gdXNlSW5wdXRUYWJsZVJvdzxhbnk+KFtdLCBjcmVhdGVHaWFCYW5Sb3cpO1xyXG5cclxuICBjb25zdCB7XHJcbiAgICByb3dzOiBzb0hpZW5UYWlUYWJsZURhdGEsXHJcbiAgICBzZXRSb3dzOiBzZXRTb0hpZW5UYWlSb3dzLFxyXG4gICAgaGFuZGxlUm93Q2xpY2s6IGhhbmRsZVNvSGllblRhaVJvd0NsaWNrLFxyXG4gICAgaGFuZGxlQWRkUm93OiBvcmlnaW5hbEhhbmRsZVNvSGllblRhaUFkZFJvdyxcclxuICAgIGhhbmRsZURlbGV0ZVJvdzogaGFuZGxlU29IaWVuVGFpRGVsZXRlUm93LFxyXG4gICAgaGFuZGxlQ29weVJvdzogaGFuZGxlU29IaWVuVGFpQ29weVJvdyxcclxuICAgIGhhbmRsZVBhc3RlUm93OiBoYW5kbGVTb0hpZW5UYWlQYXN0ZVJvdyxcclxuICAgIGhhbmRsZU1vdmVSb3c6IGhhbmRsZVNvSGllblRhaU1vdmVSb3csXHJcbiAgICBoYW5kbGVDZWxsVmFsdWVDaGFuZ2U6IG9yaWdpbmFsSGFuZGxlU29IaWVuVGFpQ2VsbFZhbHVlQ2hhbmdlXHJcbiAgfSA9IHVzZUlucHV0VGFibGVSb3c8YW55PihbXSwgY3JlYXRlU29IaWVuVGFpUm93KTtcclxuXHJcbiAgLy8gV3JhcCBoYW5kbGVBZGRSb3cgdG8gYWRkIGxvZ2dpbmdcclxuICBjb25zdCBoYW5kbGVBZGRSb3cgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBvcmlnaW5hbEhhbmRsZUFkZFJvdygpO1xyXG4gIH0sIFtvcmlnaW5hbEhhbmRsZUFkZFJvd10pO1xyXG5cclxuICAvLyBXcmFwIGhhbmRsZUNlbGxWYWx1ZUNoYW5nZSB0byBhZGQgbG9nZ2luZ1xyXG4gIGNvbnN0IGhhbmRsZUNlbGxWYWx1ZUNoYW5nZSA9IHVzZUNhbGxiYWNrKFxyXG4gICAgKHJvd1V1aWQ6IHN0cmluZywgZmllbGQ6IHN0cmluZywgbmV3VmFsdWU6IGFueSkgPT4ge1xyXG4gICAgICBvcmlnaW5hbEhhbmRsZUNlbGxWYWx1ZUNoYW5nZShyb3dVdWlkLCBmaWVsZCwgbmV3VmFsdWUpO1xyXG4gICAgfSxcclxuICAgIFtvcmlnaW5hbEhhbmRsZUNlbGxWYWx1ZUNoYW5nZV1cclxuICApO1xyXG5cclxuICAvLyBXcmFwIGhhbmRsZXJzIGZvciBcIlPhu5EgaGnhu4duIHThuqFpXCIgdGFiXHJcbiAgY29uc3QgaGFuZGxlU29IaWVuVGFpQWRkUm93ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgb3JpZ2luYWxIYW5kbGVTb0hpZW5UYWlBZGRSb3coKTtcclxuICB9LCBbb3JpZ2luYWxIYW5kbGVTb0hpZW5UYWlBZGRSb3ddKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU29IaWVuVGFpQ2VsbFZhbHVlQ2hhbmdlID0gdXNlQ2FsbGJhY2soXHJcbiAgICAocm93VXVpZDogc3RyaW5nLCBmaWVsZDogc3RyaW5nLCBuZXdWYWx1ZTogYW55KSA9PiB7XHJcbiAgICAgIG9yaWdpbmFsSGFuZGxlU29IaWVuVGFpQ2VsbFZhbHVlQ2hhbmdlKHJvd1V1aWQsIGZpZWxkLCBuZXdWYWx1ZSk7XHJcbiAgICB9LFxyXG4gICAgW29yaWdpbmFsSGFuZGxlU29IaWVuVGFpQ2VsbFZhbHVlQ2hhbmdlXVxyXG4gICk7XHJcblxyXG4gIC8vIFN0YXRlIGZvciBzdG9yaW5nIHNlbGVjdGVkIGRhdGEgZm9sbG93aW5nIEdlbmVyYWxUYWIgcGF0dGVyblxyXG4gIGNvbnN0IFtzZWxlY3RlZENodW5nVHVEYXRhLCBzZXRTZWxlY3RlZENodW5nVHVEYXRhXSA9IHVzZVN0YXRlPFJlY29yZDxzdHJpbmcsIENodW5nVHU+Pih7fSk7XHJcbiAgY29uc3QgW3NlbGVjdGVkVXNlckRhdGEsIHNldFNlbGVjdGVkVXNlckRhdGFdID0gdXNlU3RhdGU8UmVjb3JkPHN0cmluZywgVXNlcj4+KHt9KTtcclxuXHJcbiAgLy8gQ2FsbGJhY2sgaGFuZGxlcnMgZm9sbG93aW5nIEdlbmVyYWxUYWIgcGF0dGVyblxyXG4gIGNvbnN0IGhhbmRsZUNodW5nVHVTZWxlY3QgPSB1c2VDYWxsYmFjaygocm93VXVpZDogc3RyaW5nLCBjaHVuZ1R1OiBDaHVuZ1R1KSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZENodW5nVHVEYXRhKHByZXYgPT4ge1xyXG4gICAgICBjb25zdCB1cGRhdGVkID0ge1xyXG4gICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgW3Jvd1V1aWRdOiBjaHVuZ1R1XHJcbiAgICAgIH07XHJcbiAgICAgIHJldHVybiB1cGRhdGVkO1xyXG4gICAgfSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBoYW5kbGVVc2VyU2VsZWN0ID0gdXNlQ2FsbGJhY2soKHJvd1V1aWQ6IHN0cmluZywgdXNlcjogVXNlcikgPT4ge1xyXG4gICAgc2V0U2VsZWN0ZWRVc2VyRGF0YShwcmV2ID0+IHtcclxuICAgICAgY29uc3QgdXBkYXRlZCA9IHtcclxuICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgIFtyb3dVdWlkXTogdXNlclxyXG4gICAgICB9O1xyXG5cclxuICAgICAgcmV0dXJuIHVwZGF0ZWQ7XHJcbiAgICB9KTtcclxuICB9LCBbXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IGFzeW5jIChkYXRhOiBhbnkpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCdGb3JtIGRhdGE+Pj4+Pj4+Pj4+PjonLCBkYXRhKTtcclxuICAgIGNvbnNvbGUubG9nKCdUYWJsZSBkYXRhPj4+Pj4+Pj4+Pj46JywgdGFibGVEYXRhKTtcclxuXHJcbiAgICAvLyBDcmVhdGUgY29tYmluZWQgbGlzdCB3aXRoIG1hX2N0IChVVUlEKSBhbmQgdXNlcm5hbWUgZm9ybWF0IGZvciBcIkNo4bupbmcgdOG7qyAmIE5nxrDhu51pIHPhu60gZOG7pW5nXCIgdGFiXHJcbiAgICBjb25zdCBjb21iaW5lZExpc3QgPSB0YWJsZURhdGEubWFwKHJvdyA9PiB7XHJcbiAgICAgIGNvbnN0IGNodW5nVHUgPSBzZWxlY3RlZENodW5nVHVEYXRhW3Jvdy51dWlkXTtcclxuICAgICAgY29uc3QgdXNlciA9IHNlbGVjdGVkVXNlckRhdGFbcm93LnV1aWRdO1xyXG5cclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBtYV9jdDogY2h1bmdUdT8udXVpZCB8fCAnJywgLy8gVXNlIENodW5nVHUgVVVJRCBpbnN0ZWFkIG9mIG1hX2N0XHJcbiAgICAgICAgdXNlcm5hbWU6IHVzZXI/LnVzZXJuYW1lIHx8IHJvdy51c2VybmFtZSB8fCAnJ1xyXG4gICAgICB9O1xyXG4gICAgfSk7XHJcblxyXG4gICAgLy8gQ3JlYXRlIGNvbWJpbmVkIGxpc3QgZm9yIFwiU+G7kSBoaeG7h24gdOG6oWlcIiB0YWJcclxuICAgIGNvbnN0IHNvSGllblRhaUNvbWJpbmVkTGlzdCA9IHNvSGllblRhaVRhYmxlRGF0YS5tYXAocm93ID0+IHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBuZ2F5OiByb3cubmdheSB8fCAnJyxcclxuICAgICAgICBzb19oaWVuX3RhaTogcm93LnNvX2hpZW5fdGFpIHx8ICcnXHJcbiAgICAgIH07XHJcbiAgICB9KTtcclxuXHJcbiAgICAvLyBMb2cgdGhlIGNvbWJpbmVkIGxpc3RzIGluIHJlcXVlc3RlZCBmb3JtYXRcclxuICAgIGNvbnNvbGUubG9nKCfwn5OL8J+RpSBMaXN0IG9mIENodW5nVHUgYW5kIFVzZXI6JywgY29tYmluZWRMaXN0KTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBpZiAob25TdWJtaXQpIHtcclxuICAgICAgICBjb25zdCBmb3JtRGF0YSA9IHtcclxuICAgICAgICAgIC4uLmRhdGEsXHJcbiAgICAgICAgICBkYW5oX3NhY2hfY2h1bmdfdHU6IGNvbWJpbmVkTGlzdCxcclxuICAgICAgICAgIGRhbmhfc2FjaF9zb19oaWVuX3RhaTogc29IaWVuVGFpQ29tYmluZWRMaXN0XHJcbiAgICAgICAgfTtcclxuICAgICAgICBhd2FpdCBvblN1Ym1pdChmb3JtRGF0YSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0RXJyb3IoJ0tow7RuZyB0aOG7gyBsxrB1IGThu68gbGnhu4d1OiBLaMO0bmcgY8OzIHjhu60gbMO9IHN1Ym1pdCcpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnI6IGFueSkge1xyXG4gICAgICBzZXRFcnJvcihlcnIubWVzc2FnZSB8fCAnQ8OzIGzhu5dpIHjhuqN5IHJhJyk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ2xvc2VEaWFsb2cgPSAoKSA9PiB7XHJcbiAgICBzZXRTaG93Q29uZmlybURpYWxvZyhmYWxzZSk7XHJcbiAgICBvbkNsb3NlKCk7XHJcbiAgfTtcclxuXHJcbiAgLy8gRWZmZWN0IHRvIHVwZGF0ZSBmb3JtIGZpZWxkcyB3aGVuIGluaXRpYWxEYXRhIGNoYW5nZXNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGluaXRpYWxEYXRhKSB7XHJcbiAgICAgIC8vIFByb2Nlc3MgaW5pdGlhbERhdGEuZGFuaF9zYWNoX2NodW5nX3R1IGlmIGF2YWlsYWJsZVxyXG4gICAgICBpZiAoXHJcbiAgICAgICAgaW5pdGlhbERhdGEuZGFuaF9zYWNoX2NodW5nX3R1ICYmXHJcbiAgICAgICAgQXJyYXkuaXNBcnJheShpbml0aWFsRGF0YS5kYW5oX3NhY2hfY2h1bmdfdHUpICYmXHJcbiAgICAgICAgaW5pdGlhbERhdGEuZGFuaF9zYWNoX2NodW5nX3R1Lmxlbmd0aCA+IDBcclxuICAgICAgKSB7XHJcbiAgICAgICAgLy8gQ29udmVydCBkYW5oX3NhY2hfY2h1bmdfdHUgYXJyYXkgdG8gdGhlIGZvcm1hdCBleHBlY3RlZCBieSB0aGUgdGFibGVcclxuICAgICAgICBjb25zdCB0YWJsZVJvd3MgPSBpbml0aWFsRGF0YS5kYW5oX3NhY2hfY2h1bmdfdHUubWFwKChpdGVtOiBhbnkpID0+IHtcclxuICAgICAgICAgIC8vIENyZWF0ZSBhIG5ldyByb3cgd2l0aCBhIHVuaXF1ZSBVVUlEXHJcbiAgICAgICAgICBjb25zdCBuZXdSb3cgPSBjcmVhdGVHaWFCYW5Sb3coKTtcclxuXHJcbiAgICAgICAgICAvLyBTZXQgdGhlIHZhbHVlcyBmcm9tIHRoZSBkYW5oX3NhY2hfY2h1bmdfdHUgaXRlbVxyXG4gICAgICAgICAgbmV3Um93Lm1hX2N0ID0gaXRlbS5tYV9jdCB8fCAnJztcclxuICAgICAgICAgIG5ld1Jvdy50ZW5fY3QgPSBpdGVtLnRlbl9jdCB8fCAnJztcclxuICAgICAgICAgIG5ld1Jvdy51c2VyX2lkID0gaXRlbS5pZCB8fCAnJztcclxuICAgICAgICAgIG5ld1Jvdy51c2VybmFtZSA9IGl0ZW0udXNlcm5hbWUgfHwgJyc7XHJcbiAgICAgICAgICBuZXdSb3cuZmlyc3RfbmFtZSA9IGl0ZW0uZmlyc3RfbmFtZSB8fCAnJztcclxuXHJcbiAgICAgICAgICAvLyBTdG9yZSB0aGUgc2VsZWN0ZWQgZGF0YSBmb3IgZm9ybSBzdWJtaXNzaW9uIChmb2xsb3dpbmcgR2VuZXJhbFRhYiBwYXR0ZXJuKVxyXG4gICAgICAgICAgaWYgKGl0ZW0ubWFfY3QpIHtcclxuICAgICAgICAgICAgc2V0U2VsZWN0ZWRDaHVuZ1R1RGF0YShwcmV2ID0+ICh7XHJcbiAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICBbbmV3Um93LnV1aWRdOiBpdGVtIC8vIFN0b3JlIHRoZSBmdWxsIGl0ZW0gZGF0YVxyXG4gICAgICAgICAgICB9KSk7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgaWYgKGl0ZW0udXNlcm5hbWUpIHtcclxuICAgICAgICAgICAgc2V0U2VsZWN0ZWRVc2VyRGF0YShwcmV2ID0+ICh7XHJcbiAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICBbbmV3Um93LnV1aWRdOiBpdGVtIC8vIFN0b3JlIHRoZSBmdWxsIGl0ZW0gZGF0YVxyXG4gICAgICAgICAgICB9KSk7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgcmV0dXJuIG5ld1JvdztcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgLy8gU2V0IHRoZSB0YWJsZSBkYXRhXHJcbiAgICAgICAgaWYgKHRhYmxlUm93cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAvLyBVc2UgdGhlIHNldFJvd3MgZnVuY3Rpb24gZnJvbSB1c2VJbnB1dFRhYmxlUm93XHJcbiAgICAgICAgICBzZXRSb3dzKHRhYmxlUm93cyk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBQcm9jZXNzIGluaXRpYWxEYXRhLmRhbmhfc2FjaF9zb19oaWVuX3RhaSBpZiBhdmFpbGFibGVcclxuICAgICAgaWYgKFxyXG4gICAgICAgIGluaXRpYWxEYXRhLmRhbmhfc2FjaF9zb19oaWVuX3RhaSAmJlxyXG4gICAgICAgIEFycmF5LmlzQXJyYXkoaW5pdGlhbERhdGEuZGFuaF9zYWNoX3NvX2hpZW5fdGFpKSAmJlxyXG4gICAgICAgIGluaXRpYWxEYXRhLmRhbmhfc2FjaF9zb19oaWVuX3RhaS5sZW5ndGggPiAwXHJcbiAgICAgICkge1xyXG4gICAgICAgIC8vIENvbnZlcnQgZGFuaF9zYWNoX3NvX2hpZW5fdGFpIGFycmF5IHRvIHRoZSBmb3JtYXQgZXhwZWN0ZWQgYnkgdGhlIHRhYmxlXHJcbiAgICAgICAgY29uc3Qgc29IaWVuVGFpVGFibGVSb3dzID0gaW5pdGlhbERhdGEuZGFuaF9zYWNoX3NvX2hpZW5fdGFpLm1hcCgoaXRlbTogYW55KSA9PiB7XHJcbiAgICAgICAgICAvLyBDcmVhdGUgYSBuZXcgcm93IHdpdGggYSB1bmlxdWUgVVVJRFxyXG4gICAgICAgICAgY29uc3QgbmV3Um93ID0gY3JlYXRlU29IaWVuVGFpUm93KCk7XHJcblxyXG4gICAgICAgICAgLy8gU2V0IHRoZSB2YWx1ZXMgZnJvbSB0aGUgZGFuaF9zYWNoX3NvX2hpZW5fdGFpIGl0ZW1cclxuICAgICAgICAgIG5ld1Jvdy5uZ2F5ID0gaXRlbS5uZ2F5IHx8ICcnO1xyXG4gICAgICAgICAgbmV3Um93LnNvX2hpZW5fdGFpID0gaXRlbS5zb19oaWVuX3RhaSB8fCAnJztcclxuXHJcbiAgICAgICAgICByZXR1cm4gbmV3Um93O1xyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICAvLyBTZXQgdGhlIHRhYmxlIGRhdGEgZm9yIFwiU+G7kSBoaeG7h24gdOG6oWlcIiB0YWJcclxuICAgICAgICBpZiAoc29IaWVuVGFpVGFibGVSb3dzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgIHNldFNvSGllblRhaVJvd3Moc29IaWVuVGFpVGFibGVSb3dzKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LCBbaW5pdGlhbERhdGEsIHNldFJvd3MsIHNldFNvSGllblRhaVJvd3MsIGNyZWF0ZUdpYUJhblJvdywgY3JlYXRlU29IaWVuVGFpUm93XSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8QXJpdG9EaWFsb2dcclxuICAgICAgICBvcGVuPXtvcGVufVxyXG4gICAgICAgIG9uQ2xvc2U9e29uQ2xvc2V9XHJcbiAgICAgICAgdGl0bGU9e21vZGUgPT09ICdhZGQnID8gJ03hu5tpJyA6IG1vZGUgPT09ICdlZGl0JyA/ICdT4butYScgOiAnWGVtJ31cclxuICAgICAgICBtYXhXaWR0aD0nbGcnXHJcbiAgICAgICAgZGlzYWJsZUJhY2tkcm9wQ2xvc2U9e2ZhbHNlfVxyXG4gICAgICAgIGRpc2FibGVFc2NhcGVLZXlEb3duPXtmYWxzZX1cclxuICAgICAgICB0aXRsZUljb249ezxBcml0b0ljb24gaWNvbj17MjgxfSAvPn1cclxuICAgICAgPlxyXG4gICAgICAgIDxBcml0b0Zvcm1cclxuICAgICAgICAgIG1vZGU9e21vZGV9XHJcbiAgICAgICAgICBoYXNBcml0b0FjdGlvbkJhcj17ZmFsc2V9XHJcbiAgICAgICAgICBzY2hlbWE9e2Zvcm1TY2hlbWF9XHJcbiAgICAgICAgICBvblN1Ym1pdD17ZGF0YSA9PiB7XHJcbiAgICAgICAgICAgIGhhbmRsZVN1Ym1pdChkYXRhKTtcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBpbml0aWFsRGF0YT17aW5pdGlhbERhdGF9XHJcbiAgICAgICAgICBjbGFzc05hbWU9J3ctWzUwdnddJ1xyXG4gICAgICAgICAgaGVhZGVyRmllbGRzPXs8QmFzaWNGb3JtIG1vZGU9e21vZGV9IC8+fVxyXG4gICAgICAgICAgY2xhc3NOYW1lQm90dG9tQmFyPSdyZWxhdGl2ZSB3LWZ1bGwgZmxleCBqdXN0aWZ5LWVuZCBnYXAtMidcclxuICAgICAgICAgIHRhYnM9e1tcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgIGlkOiAnMScsXHJcbiAgICAgICAgICAgICAgbGFiZWw6ICdUaMO0bmcgdGluIGNodW5nJyxcclxuICAgICAgICAgICAgICBjb21wb25lbnQ6IDxHZW5lcmFsVGFiIG1vZGU9e21vZGV9IGRvblZpPXtkb25WaX0gc2V0RG9uVmk9e3NldERvblZpfSAvPlxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgaWQ6ICczJyxcclxuICAgICAgICAgICAgICBsYWJlbDogJ0No4bupbmcgdOG7qyAmIE5nxrDhu51pIHPhu60gZOG7pW5nJyxcclxuICAgICAgICAgICAgICBjb21wb25lbnQ6IChcclxuICAgICAgICAgICAgICAgIDxJbnB1dFRhYmxlVGFiXHJcbiAgICAgICAgICAgICAgICAgIG1vZGU9e21vZGV9XHJcbiAgICAgICAgICAgICAgICAgIHJvd3M9e3RhYmxlRGF0YX1cclxuICAgICAgICAgICAgICAgICAgb25Sb3dDbGljaz17aGFuZGxlUm93Q2xpY2t9XHJcbiAgICAgICAgICAgICAgICAgIG9uQWRkUm93PXtoYW5kbGVBZGRSb3d9XHJcbiAgICAgICAgICAgICAgICAgIG9uRGVsZXRlUm93PXtoYW5kbGVEZWxldGVSb3d9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ29weVJvdz17aGFuZGxlQ29weVJvd31cclxuICAgICAgICAgICAgICAgICAgb25QYXN0ZVJvdz17aGFuZGxlUGFzdGVSb3d9XHJcbiAgICAgICAgICAgICAgICAgIG9uTW92ZVJvdz17aGFuZGxlTW92ZVJvd31cclxuICAgICAgICAgICAgICAgICAgb25DZWxsVmFsdWVDaGFuZ2U9e2hhbmRsZUNlbGxWYWx1ZUNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgb25DaHVuZ1R1U2VsZWN0PXtoYW5kbGVDaHVuZ1R1U2VsZWN0fVxyXG4gICAgICAgICAgICAgICAgICBvblVzZXJTZWxlY3Q9e2hhbmRsZVVzZXJTZWxlY3R9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgIGlkOiAnNCcsXHJcbiAgICAgICAgICAgICAgbGFiZWw6ICdT4buRIGhp4buHbiB04bqhaScsXHJcbiAgICAgICAgICAgICAgY29tcG9uZW50OiAoXHJcbiAgICAgICAgICAgICAgICA8U29IaWVuVGFpVGFiXHJcbiAgICAgICAgICAgICAgICAgIG1vZGU9e21vZGV9XHJcbiAgICAgICAgICAgICAgICAgIHJvd3M9e3NvSGllblRhaVRhYmxlRGF0YX1cclxuICAgICAgICAgICAgICAgICAgb25Sb3dDbGljaz17aGFuZGxlU29IaWVuVGFpUm93Q2xpY2t9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2VsbFZhbHVlQ2hhbmdlPXtoYW5kbGVTb0hpZW5UYWlDZWxsVmFsdWVDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgXX1cclxuICAgICAgICAgIGJvdHRvbUJhcj17XHJcbiAgICAgICAgICAgIDxCb3R0b21CYXJcclxuICAgICAgICAgICAgICBtb2RlPXttb2RlfVxyXG4gICAgICAgICAgICAgIG9uQWRkPXtvbkFkZEJ1dHRvbkNsaWNrfVxyXG4gICAgICAgICAgICAgIG9uRWRpdD17b25FZGl0QnV0dG9uQ2xpY2t9XHJcbiAgICAgICAgICAgICAgb25EZWxldGU9e29uRGVsZXRlQnV0dG9uQ2xpY2t9XHJcbiAgICAgICAgICAgICAgb25Db3B5PXtvbkNvcHlCdXR0b25DbGlja31cclxuICAgICAgICAgICAgICBvbkNsb3NlPXtvbkNsb3NlfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICAge2Vycm9yICYmIDxkaXYgY2xhc3NOYW1lPSdteC00IG1iLTQgcm91bmRlZCBiZy1yZWQtMTAwIHAtMiB0ZXh0LXJlZC03MDAnPntlcnJvcn08L2Rpdj59XHJcbiAgICAgIDwvQXJpdG9EaWFsb2c+XHJcblxyXG4gICAgICA8Q29uZmlybURpYWxvZ1xyXG4gICAgICAgIG9uQ2xvc2U9e2hhbmRsZUNsb3NlRGlhbG9nfVxyXG4gICAgICAgIG9wZW49e3Nob3dDb25maXJtRGlhbG9nfVxyXG4gICAgICAgIG9uQ2xvc2VDb25maXJtRGlhbG9nPXsoKSA9PiBzZXRTaG93Q29uZmlybURpYWxvZyhmYWxzZSl9XHJcbiAgICAgIC8+XHJcbiAgICA8Lz5cclxuICApO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBGb3JtRGlhbG9nO1xyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsIkJvdHRvbUJhciIsIkFyaXRvRGlhbG9nIiwiQXJpdG9Gb3JtIiwidXNlSW5wdXRUYWJsZVJvdyIsIkFyaXRvSWNvbiIsIkNvbmZpcm1EaWFsb2ciLCJJbnB1dFRhYmxlVGFiIiwiU29IaWVuVGFpVGFiIiwiZm9ybVNjaGVtYSIsIkdlbmVyYWxUYWIiLCJCYXNpY0Zvcm0iLCJGb3JtRGlhbG9nIiwibW9kZSIsIm9wZW4iLCJvbkNsb3NlIiwib25BZGRCdXR0b25DbGljayIsIm9uQ29weUJ1dHRvbkNsaWNrIiwib25EZWxldGVCdXR0b25DbGljayIsIm9uRWRpdEJ1dHRvbkNsaWNrIiwib25TdWJtaXQiLCJpbml0aWFsRGF0YSIsInNob3dDb25maXJtRGlhbG9nIiwic2V0U2hvd0NvbmZpcm1EaWFsb2ciLCJlcnJvciIsInNldEVycm9yIiwiZG9uVmkiLCJzZXREb25WaSIsImNyZWF0ZUdpYUJhblJvdyIsIm5ld1JvdyIsInV1aWQiLCJTdHJpbmciLCJNYXRoIiwicmFuZG9tIiwibWFfY3QiLCJ0ZW5fY3QiLCJ1c2VyX2lkIiwidXNlcm5hbWUiLCJmaXJzdF9uYW1lIiwiY3JlYXRlU29IaWVuVGFpUm93IiwibmdheSIsInNvX2hpZW5fdGFpIiwicm93cyIsInRhYmxlRGF0YSIsInNldFJvd3MiLCJoYW5kbGVSb3dDbGljayIsImhhbmRsZUFkZFJvdyIsIm9yaWdpbmFsSGFuZGxlQWRkUm93IiwiaGFuZGxlRGVsZXRlUm93IiwiaGFuZGxlQ29weVJvdyIsImhhbmRsZVBhc3RlUm93IiwiaGFuZGxlTW92ZVJvdyIsImhhbmRsZUNlbGxWYWx1ZUNoYW5nZSIsIm9yaWdpbmFsSGFuZGxlQ2VsbFZhbHVlQ2hhbmdlIiwic29IaWVuVGFpVGFibGVEYXRhIiwic2V0U29IaWVuVGFpUm93cyIsImhhbmRsZVNvSGllblRhaVJvd0NsaWNrIiwib3JpZ2luYWxIYW5kbGVTb0hpZW5UYWlBZGRSb3ciLCJoYW5kbGVTb0hpZW5UYWlEZWxldGVSb3ciLCJoYW5kbGVTb0hpZW5UYWlDb3B5Um93IiwiaGFuZGxlU29IaWVuVGFpUGFzdGVSb3ciLCJoYW5kbGVTb0hpZW5UYWlNb3ZlUm93Iiwib3JpZ2luYWxIYW5kbGVTb0hpZW5UYWlDZWxsVmFsdWVDaGFuZ2UiLCJyb3dVdWlkIiwiZmllbGQiLCJuZXdWYWx1ZSIsImhhbmRsZVNvSGllblRhaUFkZFJvdyIsImhhbmRsZVNvSGllblRhaUNlbGxWYWx1ZUNoYW5nZSIsInNlbGVjdGVkQ2h1bmdUdURhdGEiLCJzZXRTZWxlY3RlZENodW5nVHVEYXRhIiwic2VsZWN0ZWRVc2VyRGF0YSIsInNldFNlbGVjdGVkVXNlckRhdGEiLCJoYW5kbGVDaHVuZ1R1U2VsZWN0IiwiY2h1bmdUdSIsInByZXYiLCJ1cGRhdGVkIiwiaGFuZGxlVXNlclNlbGVjdCIsInVzZXIiLCJoYW5kbGVTdWJtaXQiLCJkYXRhIiwiY29uc29sZSIsImxvZyIsImNvbWJpbmVkTGlzdCIsIm1hcCIsInJvdyIsInNvSGllblRhaUNvbWJpbmVkTGlzdCIsImZvcm1EYXRhIiwiZGFuaF9zYWNoX2NodW5nX3R1IiwiZGFuaF9zYWNoX3NvX2hpZW5fdGFpIiwiZXJyIiwibWVzc2FnZSIsImhhbmRsZUNsb3NlRGlhbG9nIiwiQXJyYXkiLCJpc0FycmF5IiwibGVuZ3RoIiwidGFibGVSb3dzIiwiaXRlbSIsImlkIiwic29IaWVuVGFpVGFibGVSb3dzIiwidGl0bGUiLCJtYXhXaWR0aCIsImRpc2FibGVCYWNrZHJvcENsb3NlIiwiZGlzYWJsZUVzY2FwZUtleURvd24iLCJ0aXRsZUljb24iLCJpY29uIiwiaGFzQXJpdG9BY3Rpb25CYXIiLCJzY2hlbWEiLCJjbGFzc05hbWUiLCJoZWFkZXJGaWVsZHMiLCJjbGFzc05hbWVCb3R0b21CYXIiLCJ0YWJzIiwibGFiZWwiLCJjb21wb25lbnQiLCJvblJvd0NsaWNrIiwib25BZGRSb3ciLCJvbkRlbGV0ZVJvdyIsIm9uQ29weVJvdyIsIm9uUGFzdGVSb3ciLCJvbk1vdmVSb3ciLCJvbkNlbGxWYWx1ZUNoYW5nZSIsIm9uQ2h1bmdUdVNlbGVjdCIsIm9uVXNlclNlbGVjdCIsImJvdHRvbUJhciIsIm9uQWRkIiwib25FZGl0Iiwib25EZWxldGUiLCJvbkNvcHkiLCJkaXYiLCJvbkNsb3NlQ29uZmlybURpYWxvZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx\n"));

/***/ })

});