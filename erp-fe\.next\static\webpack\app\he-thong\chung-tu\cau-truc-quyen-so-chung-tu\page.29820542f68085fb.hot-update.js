"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/SoHienTaiActionBar.tsx":
/*!*************************************************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/SoHienTaiActionBar.tsx ***!
  \*************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SoHienTaiActionBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton,Tooltip!=!@mui/material */ \"(app-pages-browser)/./node_modules/.pnpm/@mui+material@6.4.11_@emoti_fdb03c9cf50ce9da1a20b3d9c4752c0d/node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_IconButton_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=IconButton,Tooltip!=!@mui/material */ \"(app-pages-browser)/./node_modules/.pnpm/@mui+material@6.4.11_@emoti_fdb03c9cf50ce9da1a20b3d9c4752c0d/node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _components_custom_arito_custom_input_table_components_action_button_set__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/custom/arito/custom-input-table/components/action-button-set */ \"(app-pages-browser)/./src/components/custom/arito/custom-input-table/components/action-button-set/index.tsx\");\n/* harmony import */ var _components_custom_arito_icon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/icon */ \"(app-pages-browser)/./src/components/custom/arito/icon/index.tsx\");\n\n\n\n\nconst renderActionButton = (button)=>{\n    let icon;\n    let title;\n    if (button.icon !== undefined && button.title) {\n        icon = button.icon;\n        title = button.title;\n    } else {\n        const config = _components_custom_arito_custom_input_table_components_action_button_set__WEBPACK_IMPORTED_MODULE_1__.buttonConfigs[button.type] || {\n            icon: 0,\n            title: \"\"\n        };\n        icon = config.icon;\n        title = config.title;\n    }\n    const isDisabled = button.disabled || false;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: title,\n        arrow: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconButton_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"small\",\n                onClick: button.onClick,\n                disabled: isDisabled,\n                sx: _components_custom_arito_custom_input_table_components_action_button_set__WEBPACK_IMPORTED_MODULE_1__.buttonSx,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_icon__WEBPACK_IMPORTED_MODULE_2__.AritoIcon, {\n                    icon: icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\nfunction SoHienTaiActionBar(param) {\n    let { mode } = param;\n    const buttonTypes = [\n        \"pin\"\n    ];\n    const buttonActions = {\n        pin: ()=>console.log(\"Pin clicked\")\n    };\n    const buttons = buttonTypes.map((type)=>({\n            type,\n            onClick: buttonActions[type],\n            disabled: false\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-9 flex-none items-center justify-between border-b border-gray-200 bg-white px-1 py-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-full flex-1 items-center justify-between\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: buttons.map((button, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: renderActionButton(button)\n                    }, button.type + index, false, {\n                        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\SoHienTaiTab\\\\SoHienTaiActionBar.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n_c = SoHienTaiActionBar;\nvar _c;\n$RefreshReg$(_c, \"SoHienTaiActionBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/SoHienTaiActionBar.tsx\n"));

/***/ })

});